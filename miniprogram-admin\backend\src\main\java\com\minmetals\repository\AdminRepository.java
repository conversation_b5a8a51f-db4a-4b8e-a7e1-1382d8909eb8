package com.minmetals.repository;

import com.minmetals.entity.Admin;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 管理员数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface AdminRepository extends JpaRepository<Admin, Long> {

    /**
     * 根据用户名查找管理员
     */
    Optional<Admin> findByUsername(String username);

    /**
     * 查找所有启用的管理员
     */
    List<Admin> findByEnabledTrueOrderByCreatedAtDesc();

    /**
     * 根据角色查找管理员
     */
    List<Admin> findByRoleAndEnabledTrueOrderByCreatedAtDesc(String role);

    /**
     * 模糊搜索管理员（姓名、用户名、邮箱）
     */
    @Query("SELECT a FROM Admin a WHERE a.enabled = true AND " +
           "(LOWER(a.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.username) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.email) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY a.createdAt DESC")
    List<Admin> searchAdmins(@Param("keyword") String keyword);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查用户名是否存在（排除指定ID）
     */
    boolean existsByUsernameAndIdNot(String username, Long id);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查邮箱是否存在（排除指定ID）
     */
    boolean existsByEmailAndIdNot(String email, Long id);
}
