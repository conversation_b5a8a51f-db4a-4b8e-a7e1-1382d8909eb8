package com.minmetals.service.impl;

import com.minmetals.dto.AdminCreateRequest;
import com.minmetals.dto.AdminDTO;
import com.minmetals.dto.AdminUpdateRequest;
import com.minmetals.entity.Admin;
import com.minmetals.repository.AdminRepository;
import com.minmetals.service.AdminService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 管理员服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class AdminServiceImpl implements AdminService {

    private final AdminRepository adminRepository;

    @Override
    public Admin findByUsername(String username) {
        log.debug("查找用户名为 {} 的管理员", username);
        return adminRepository.findByUsername(username).orElse(null);
    }

    @Override
    public Admin findById(Long id) {
        log.debug("查找ID为 {} 的管理员", id);
        return adminRepository.findById(id).orElse(null);
    }

    @Override
    public List<AdminDTO> getAllAdmins() {
        log.debug("获取所有启用的管理员列表");
        List<Admin> admins = adminRepository.findByEnabledTrueOrderByCreatedAtDesc();
        return convertToDTOList(admins);
    }

    @Override
    public AdminDTO getAdminById(Long id) {
        log.debug("获取ID为 {} 的管理员详情", id);
        Admin admin = adminRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("管理员不存在: " + id));
        return convertToDTO(admin);
    }

    @Override
    public List<AdminDTO> searchAdmins(String keyword) {
        log.debug("搜索管理员，关键词: {}", keyword);
        if (!StringUtils.hasText(keyword)) {
            return getAllAdmins();
        }
        List<Admin> admins = adminRepository.searchAdmins(keyword.trim());
        return convertToDTOList(admins);
    }

    @Override
    public List<AdminDTO> getAdminsByRole(String role) {
        log.debug("根据角色 {} 获取管理员列表", role);
        List<Admin> admins = adminRepository.findByRoleAndEnabledTrueOrderByCreatedAtDesc(role);
        return convertToDTOList(admins);
    }

    @Override
    @Transactional
    public AdminDTO createAdmin(AdminCreateRequest request) {
        log.debug("创建管理员: {}", request.getUsername());

        // 检查用户名是否已存在
        if (adminRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在: " + request.getUsername());
        }

        // 检查邮箱是否已存在
        if (StringUtils.hasText(request.getEmail()) && adminRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("邮箱已存在: " + request.getEmail());
        }

        Admin admin = new Admin();
        BeanUtils.copyProperties(request, admin);

        admin = adminRepository.save(admin);
        log.info("成功创建管理员: {}", admin.getUsername());

        return convertToDTO(admin);
    }

    @Override
    @Transactional
    public AdminDTO updateAdmin(Long id, AdminUpdateRequest request) {
        log.debug("更新管理员: {}", id);

        Admin admin = adminRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("管理员不存在: " + id));

        // 检查邮箱是否已存在（排除当前管理员）
        if (StringUtils.hasText(request.getEmail()) && 
            adminRepository.existsByEmailAndIdNot(request.getEmail(), id)) {
            throw new RuntimeException("邮箱已存在: " + request.getEmail());
        }

        // 更新字段
        if (StringUtils.hasText(request.getName())) {
            admin.setName(request.getName());
        }
        if (StringUtils.hasText(request.getPassword())) {
            admin.setPassword(request.getPassword());
        }
        if (StringUtils.hasText(request.getEmail())) {
            admin.setEmail(request.getEmail());
        }
        if (StringUtils.hasText(request.getPhone())) {
            admin.setPhone(request.getPhone());
        }
        if (StringUtils.hasText(request.getRole())) {
            admin.setRole(request.getRole());
        }
        if (request.getEnabled() != null) {
            admin.setEnabled(request.getEnabled());
        }
        if (StringUtils.hasText(request.getDescription())) {
            admin.setDescription(request.getDescription());
        }

        admin = adminRepository.save(admin);
        log.info("成功更新管理员: {}", admin.getUsername());

        return convertToDTO(admin);
    }

    @Override
    @Transactional
    public void deleteAdmin(Long id) {
        log.debug("删除管理员: {}", id);

        Admin admin = adminRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("管理员不存在: " + id));

        adminRepository.delete(admin);
        log.info("成功删除管理员: {}", admin.getUsername());
    }

    @Override
    @Transactional
    public AdminDTO toggleAdminStatus(Long id) {
        log.debug("切换管理员状态: {}", id);

        Admin admin = adminRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("管理员不存在: " + id));

        admin.setEnabled(!admin.getEnabled());
        admin = adminRepository.save(admin);

        log.info("成功切换管理员 {} 状态为: {}", admin.getUsername(), admin.getEnabled());

        return convertToDTO(admin);
    }

    @Override
    public boolean validateLogin(String username, String password) {
        log.debug("验证管理员登录: {}", username);
        Admin admin = findByUsername(username);
        if (admin == null || !admin.getEnabled()) {
            return false;
        }
        return admin.getPassword().equals(password);
    }

    @Override
    @Transactional
    public void updateLastLoginTime(String username) {
        log.debug("更新管理员最后登录时间: {}", username);
        Admin admin = findByUsername(username);
        if (admin != null) {
            admin.setLastLoginTime(LocalDateTime.now());
            adminRepository.save(admin);
        }
    }

    @Override
    public AdminDTO convertToDTO(Admin admin) {
        if (admin == null) {
            return null;
        }

        AdminDTO dto = new AdminDTO();
        BeanUtils.copyProperties(admin, dto);
        return dto;
    }

    @Override
    public List<AdminDTO> convertToDTOList(List<Admin> admins) {
        List<AdminDTO> dtoList = new ArrayList<>();
        for (Admin admin : admins) {
            AdminDTO dto = convertToDTO(admin);
            if (dto != null) {
                dtoList.add(dto);
            }
        }
        return dtoList;
    }
}
