package com.minmetals.service;

import com.minmetals.dto.LongImageDTO;
import com.minmetals.entity.LongImage;

import java.util.List;

/**
 * 长图展示服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface LongImageService {

    /**
     * 根据ID获取长图详情
     */
    LongImageDTO getById(Long id);

    /**
     * 获取所有启用的长图
     */
    List<LongImageDTO> getAllEnabled();

    /**
     * 根据分类获取长图
     */
    List<LongImageDTO> getByCategory(String category);

    /**
     * 创建长图
     */
    LongImageDTO createLongImage(LongImageDTO dto);

    /**
     * 更新长图
     */
    LongImageDTO updateLongImage(Long id, LongImageDTO dto);

    /**
     * 删除长图
     */
    void deleteLongImage(Long id);

    /**
     * 启用/禁用长图
     */
    LongImageDTO toggleStatus(Long id);

    /**
     * 根据标题搜索长图
     */
    List<LongImageDTO> searchByTitle(String keyword);

    /**
     * 根据分类搜索长图
     */
    List<LongImageDTO> searchByCategory(String keyword);

    /**
     * 根据创建者获取长图
     */
    List<LongImageDTO> getByCreator(String creator);

    /**
     * 获取所有分类
     */
    List<String> getAllCategories();

    /**
     * 将实体转换为DTO
     */
    LongImageDTO convertToDTO(LongImage entity);

    /**
     * 将实体列表转换为DTO列表
     */
    List<LongImageDTO> convertToDTOList(List<LongImage> entities);
}
