package com.minmetals.controller;

import com.minmetals.dto.*;
import com.minmetals.entity.BackgroundImage;
import com.minmetals.service.BackgroundImageService;
import com.minmetals.service.FileUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 背景图片管理控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/background-images")
@RequiredArgsConstructor
@Tag(name = "背景图片管理", description = "背景图片增删改查相关接口")
public class BackgroundImageManagementController {

    private final BackgroundImageService backgroundImageService;
    private final FileUploadService fileUploadService;

    /**
     * 获取所有背景图片列表（包括禁用的）
     */
    @GetMapping
    @Operation(summary = "获取所有背景图片列表", description = "获取所有背景图片列表（包括禁用的）")
    public ApiResponse<List<BackgroundImage>> getAllBackgroundImages() {
        log.info("获取所有背景图片列表");
        try {
            List<BackgroundImage> images = backgroundImageService.getAllBackgroundImages();
            return ApiResponse.success("获取成功", images);
        } catch (Exception e) {
            log.error("获取背景图片列表失败", e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有启用的背景图片列表
     */
    @GetMapping("/enabled")
    @Operation(summary = "获取所有启用的背景图片列表", description = "获取所有启用的背景图片列表")
    public ApiResponse<List<BackgroundImage>> getAllEnabledBackgroundImages() {
        log.info("获取所有启用的背景图片列表");
        try {
            List<BackgroundImage> images = backgroundImageService.getAllEnabledBackgroundImages();
            return ApiResponse.success("获取成功", images);
        } catch (Exception e) {
            log.error("获取启用的背景图片列表失败", e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取背景图片详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取背景图片详情", description = "根据ID获取背景图片详情")
    public ApiResponse<BackgroundImage> getBackgroundImageById(
            @Parameter(description = "背景图片ID") @PathVariable Long id) {
        log.info("获取背景图片详情，ID: {}", id);
        try {
            BackgroundImage image = backgroundImageService.getBackgroundImageById(id);
            return ApiResponse.success("获取成功", image);
        } catch (Exception e) {
            log.error("获取背景图片详情失败，ID: {}", id, e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 搜索背景图片
     */
    @GetMapping("/search")
    @Operation(summary = "搜索背景图片", description = "根据关键词搜索背景图片")
    public ApiResponse<List<BackgroundImage>> searchBackgroundImages(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        log.info("搜索背景图片，关键词: {}", keyword);
        try {
            List<BackgroundImage> images = backgroundImageService.searchBackgroundImages(keyword);
            return ApiResponse.success("搜索成功", images);
        } catch (Exception e) {
            log.error("搜索背景图片失败，关键词: {}", keyword, e);
            return ApiResponse.error("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 创建背景图片
     */
    @PostMapping
    @Operation(summary = "创建背景图片", description = "创建新的背景图片")
    public ApiResponse<BackgroundImage> createBackgroundImage(@Valid @RequestBody BackgroundImageCreateRequest request) {
        log.info("创建背景图片，名称: {}", request.getName());
        try {
            BackgroundImage image = backgroundImageService.createBackgroundImage(request);
            return ApiResponse.success("创建成功", image);
        } catch (Exception e) {
            log.error("创建背景图片失败，名称: {}", request.getName(), e);
            return ApiResponse.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新背景图片
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新背景图片", description = "更新背景图片信息")
    public ApiResponse<BackgroundImage> updateBackgroundImage(
            @Parameter(description = "背景图片ID") @PathVariable Long id,
            @Valid @RequestBody BackgroundImageUpdateRequest request) {
        log.info("更新背景图片，ID: {}", id);
        try {
            BackgroundImage image = backgroundImageService.updateBackgroundImage(id, request);
            return ApiResponse.success("更新成功", image);
        } catch (Exception e) {
            log.error("更新背景图片失败，ID: {}", id, e);
            return ApiResponse.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除背景图片
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除背景图片", description = "删除背景图片")
    public ApiResponse<String> deleteBackgroundImage(
            @Parameter(description = "背景图片ID") @PathVariable Long id) {
        log.info("删除背景图片，ID: {}", id);
        try {
            backgroundImageService.deleteBackgroundImage(id);
            return ApiResponse.success("删除成功", "背景图片已删除");
        } catch (Exception e) {
            log.error("删除背景图片失败，ID: {}", id, e);
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用背景图片
     */
    @PatchMapping("/{id}/toggle-status")
    @Operation(summary = "启用/禁用背景图片", description = "切换背景图片的启用状态")
    public ApiResponse<BackgroundImage> toggleBackgroundImageStatus(
            @Parameter(description = "背景图片ID") @PathVariable Long id) {
        log.info("切换背景图片状态，ID: {}", id);
        try {
            BackgroundImage image = backgroundImageService.toggleBackgroundImageStatus(id);
            return ApiResponse.success("状态切换成功", image);
        } catch (Exception e) {
            log.error("切换背景图片状态失败，ID: {}", id, e);
            return ApiResponse.error("状态切换失败: " + e.getMessage());
        }
    }

    /**
     * 上传并创建背景图片
     */
    @PostMapping(value = "/upload", consumes = "multipart/form-data")
    @Operation(summary = "上传并创建背景图片", description = "上传图片文件并创建背景图片记录")
    public ApiResponse<BackgroundImage> uploadAndCreateBackgroundImage(
            @Parameter(description = "图片文件", required = true, schema = @Schema(type = "string", format = "binary"))
            @RequestPart("file") MultipartFile file,
            @Parameter(description = "图片名称", required = true)
            @RequestPart("name") String name,
            @Parameter(description = "图片描述")
            @RequestPart(value = "description", required = false) String description,
            @Parameter(description = "是否启用")
            @RequestPart(value = "enabled", required = false) String enabled,
            @Parameter(description = "排序顺序")
            @RequestPart(value = "sortOrder", required = false) String sortOrder) {

        log.info("上传并创建背景图片，文件名: {}, 名称: {}", file.getOriginalFilename(), name);

        try {
            if (file.isEmpty()) {
                return ApiResponse.error("请选择要上传的文件");
            }

            if (name == null || name.trim().isEmpty()) {
                return ApiResponse.error("图片名称不能为空");
            }

            // 上传图片文件
            String imageUrl = fileUploadService.uploadBackgroundImage(file);

            // 创建背景图片记录
            BackgroundImageCreateRequest createRequest = new BackgroundImageCreateRequest();
            createRequest.setName(name.trim());
            createRequest.setImageUrl(imageUrl);
            createRequest.setDescription(description);
            createRequest.setEnabled(enabled != null ? Boolean.parseBoolean(enabled) : true);
            createRequest.setSortOrder(sortOrder != null ? Integer.parseInt(sortOrder) : 0);

            BackgroundImage backgroundImage = backgroundImageService.createBackgroundImage(createRequest);

            return ApiResponse.success("上传并创建成功", backgroundImage);

        } catch (Exception e) {
            log.error("上传并创建背景图片失败，文件名: {}, 名称: {}", file.getOriginalFilename(), name, e);
            return ApiResponse.error("上传并创建失败: " + e.getMessage());
        }
    }
}
