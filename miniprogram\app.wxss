/**app.wxss**/
/* 全局样式 */
page {
  background-color: #dcdcdd;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 主要颜色变量 */
.primary-bg {
  background-color: #c70019;
}

.secondary-bg {
  background-color: #dcdcdd;
}

.primary-text {
  color: #221815;
}

.secondary-text {
  color: #727171;
}

.white-text {
  color: #ffffff;
}

/* 通用按钮样式 */
.btn {
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  text-align: center;
  border: none;
  outline: none;
}

.btn-primary {
  background-color: #c70019;
  color: #ffffff;
}

.btn-primary:hover {
  background-color: #a50015;
}

.btn-secondary {
  background-color: #dcdcdd;
  color: #221815;
  border: 2rpx solid #727171;
}

/* 容器样式 */
.container {
  padding: 40rpx;
}

.full-screen {
  width: 100vw;
  height: 100vh;
}

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 表单样式 */
.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #221815;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #dcdcdd;
  border-radius: 8rpx;
  font-size: 32rpx;
  background-color: #ffffff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #c70019;
  outline: none;
}

/* 列表样式 */
.list-item {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background-color: #f8f8f8;
}

/* 文本样式 */
.text-center {
  text-align: center;
}

.text-bold {
  font-weight: bold;
}

.text-large {
  font-size: 36rpx;
}

.text-medium {
  font-size: 32rpx;
}

.text-small {
  font-size: 28rpx;
}
