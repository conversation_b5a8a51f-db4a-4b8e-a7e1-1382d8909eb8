package com.minmetals.service.impl;

import com.minmetals.dto.EmployeeCreateRequest;
import com.minmetals.dto.EmployeeDTO;
import com.minmetals.dto.EmployeeUpdateRequest;
import com.minmetals.entity.Employee;
import com.minmetals.repository.EmployeeRepository;
import com.minmetals.service.EmployeeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 员工服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class EmployeeServiceImpl implements EmployeeService {

    private final EmployeeRepository employeeRepository;

    @Override
    public Employee findByUsername(String username) {
        log.debug("查找用户名为 {} 的员工", username);
        return employeeRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户名不存在: " + username));
    }

    @Override
    public Employee findById(Long id) {
        log.debug("查找ID为 {} 的员工", id);
        return employeeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("员工不存在: " + id));
    }

    @Override
    public List<EmployeeDTO> getAllEmployees() {
        log.debug("获取所有启用的员工列表");
        List<Employee> employees = employeeRepository.findByEnabledTrueOrderByCreatedAtDesc();
        return convertToDTOList(employees);
    }

    @Override
    public EmployeeDTO getEmployeeById(Long id) {
        log.debug("获取ID为 {} 的员工详情", id);
        Employee employee = findById(id);

        if (!employee.getEnabled()) {
            throw new RuntimeException("员工已被禁用: " + id);
        }

        return convertToDTO(employee);
    }



    @Override
    public List<EmployeeDTO> searchEmployees(String keyword) {
        log.debug("搜索员工，关键词: {}", keyword);

        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllEmployees();
        }

        List<Employee> employees = employeeRepository.searchEmployees(keyword.trim());
        return convertToDTOList(employees);
    }

    @Override
    public List<EmployeeDTO> getEmployeesByDepartment(String department) {
        log.debug("获取部门 {} 的员工列表", department);
        List<Employee> employees = employeeRepository.findByDepartmentAndEnabledTrueOrderByCreatedAtDesc(department);
        return convertToDTOList(employees);
    }

    @Override
    public List<EmployeeDTO> getEmployeesByPosition(String position) {
        log.debug("获取职位 {} 的员工列表", position);
        List<Employee> employees = employeeRepository.findByPositionAndEnabledTrueOrderByCreatedAtDesc(position);
        return convertToDTOList(employees);
    }

    @Override
    @Transactional
    public EmployeeDTO createEmployee(EmployeeCreateRequest request) {
        log.debug("创建员工: {}", request.getUsername());

        // 检查用户名是否已存在
        if (employeeRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在: " + request.getUsername());
        }

        // 检查员工工号是否已存在
        if (employeeRepository.existsByEmployeeId(request.getEmployeeId())) {
            throw new RuntimeException("员工工号已存在: " + request.getEmployeeId());
        }

        Employee employee = new Employee();
        BeanUtils.copyProperties(request, employee);

        employee = employeeRepository.save(employee);
        log.info("成功创建员工: {}", employee.getUsername());

        return convertToDTO(employee);
    }

    @Override
    @Transactional
    public EmployeeDTO updateEmployee(Long id, EmployeeUpdateRequest request) {
        log.debug("更新员工: {}", id);

        Employee employee = employeeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("员工不存在: " + id));

        // 更新字段
        if (StringUtils.hasText(request.getName())) {
            employee.setName(request.getName());
        }
        if (StringUtils.hasText(request.getPassword())) {
            employee.setPassword(request.getPassword());
        }
        if (StringUtils.hasText(request.getDepartment())) {
            employee.setDepartment(request.getDepartment());
        }
        if (StringUtils.hasText(request.getPosition())) {
            employee.setPosition(request.getPosition());
        }
        if (StringUtils.hasText(request.getLevel())) {
            employee.setLevel(request.getLevel());
        }
        if (StringUtils.hasText(request.getPhone())) {
            employee.setPhone(request.getPhone());
        }
        if (StringUtils.hasText(request.getEmail())) {
            employee.setEmail(request.getEmail());
        }
        if (StringUtils.hasText(request.getAddress())) {
            employee.setAddress(request.getAddress());
        }
        if (StringUtils.hasText(request.getAvatar())) {
            employee.setAvatar(request.getAvatar());
        }
        if (StringUtils.hasText(request.getManager())) {
            employee.setManager(request.getManager());
        }
        if (StringUtils.hasText(request.getWorkLocation())) {
            employee.setWorkLocation(request.getWorkLocation());
        }
        if (StringUtils.hasText(request.getDescription())) {
            employee.setDescription(request.getDescription());
        }
        if (request.getHireDate() != null) {
            employee.setHireDate(Date.valueOf(request.getHireDate()));
        }
        if (request.getEnabled() != null) {
            employee.setEnabled(request.getEnabled());
        }

        employee = employeeRepository.save(employee);
        log.info("成功更新员工: {}", employee.getUsername());

        return convertToDTO(employee);
    }

    @Override
    @Transactional
    public void deleteEmployee(Long id) {
        log.debug("删除员工: {}", id);

        Employee employee = employeeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("员工不存在: " + id));

        employeeRepository.delete(employee);
        log.info("成功删除员工: {}", employee.getUsername());
    }

    @Override
    @Transactional
    public EmployeeDTO toggleEmployeeStatus(Long id) {
        log.debug("切换员工状态: {}", id);

        Employee employee = employeeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("员工不存在: " + id));

        employee.setEnabled(!employee.getEnabled());
        employee = employeeRepository.save(employee);

        log.info("成功切换员工 {} 状态为: {}", employee.getUsername(), employee.getEnabled());

        return convertToDTO(employee);
    }

    @Override
    public EmployeeDTO convertToDTO(Employee employee) {
        if (employee == null) {
            return null;
        }

        EmployeeDTO dto = new EmployeeDTO();
        BeanUtils.copyProperties(employee, dto);

        // 不返回敏感信息
        return dto;
    }

    @Override
    public List<EmployeeDTO> convertToDTOList(List<Employee> employees) {
        if (employees == null || employees.isEmpty()) {
            return new ArrayList<>();
        }

        return employees.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<EmployeeDTO> getEmployeesByDepartmentId(Long departmentId) {
        log.info("根据部门ID获取员工列表: {}", departmentId);

        List<Employee> employees = employeeRepository.findByDepartmentId(departmentId);
        return convertToDTOList(employees);
    }

    @Override
    public List<EmployeeDTO> getEnabledEmployeesByDepartmentId(Long departmentId) {
        log.info("根据部门ID获取启用员工列表: {}", departmentId);

        List<Employee> employees = employeeRepository.findByDepartmentIdAndEnabledTrue(departmentId);
        return convertToDTOList(employees);
    }
}
