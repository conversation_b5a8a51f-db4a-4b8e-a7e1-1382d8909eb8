package com.minmetals.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 背景图片上传请求DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Schema(description = "背景图片上传请求")
public class BackgroundImageUploadRequest {

    @Schema(description = "图片文件", required = true)
    @NotNull(message = "图片文件不能为空")
    private MultipartFile file;

    @Schema(description = "图片名称", required = true)
    @NotBlank(message = "图片名称不能为空")
    @Size(max = 100, message = "图片名称长度不能超过100个字符")
    private String name;

    @Schema(description = "图片描述")
    @Size(max = 500, message = "图片描述长度不能超过500个字符")
    private String description;

    @Schema(description = "是否启用")
    private Boolean enabled = true;

    @Schema(description = "排序顺序")
    private Integer sortOrder = 0;
}
