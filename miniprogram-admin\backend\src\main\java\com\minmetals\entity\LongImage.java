package com.minmetals.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 长图展示实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "long_images")
@EntityListeners(AuditingEntityListener.class)
public class LongImage {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 长图分类（可选）
     */
    @Column(name = "category", length = 50)
    private String category;

    /**
     * 长图标题
     */
    @Column(name = "title", nullable = false, length = 100)
    private String title;

    /**
     * 长图URL
     */
    @Column(name = "image_url", nullable = false, length = 500)
    private String imageUrl;

    /**
     * 长图描述
     */
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 图片宽度（像素）
     */
    @Column(name = "width")
    private Integer width;

    /**
     * 图片高度（像素）
     */
    @Column(name = "height")
    private Integer height;

    /**
     * 文件大小（字节）
     */
    @Column(name = "file_size")
    private Long fileSize;

    /**
     * 图片格式（jpg, png, webp等）
     */
    @Column(name = "format", length = 10)
    private String format;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者
     */
    @Column(name = "creator", length = 50)
    private String creator;
}
