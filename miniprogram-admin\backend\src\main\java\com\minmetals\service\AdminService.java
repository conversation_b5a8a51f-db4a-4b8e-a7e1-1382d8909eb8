package com.minmetals.service;

import com.minmetals.dto.AdminCreateRequest;
import com.minmetals.dto.AdminDTO;
import com.minmetals.dto.AdminUpdateRequest;
import com.minmetals.entity.Admin;

import java.util.List;

/**
 * 管理员服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface AdminService {

    /**
     * 根据用户名查找管理员
     */
    Admin findByUsername(String username);

    /**
     * 根据ID查找管理员
     */
    Admin findById(Long id);

    /**
     * 获取所有启用的管理员列表
     */
    List<AdminDTO> getAllAdmins();

    /**
     * 根据ID获取管理员详情
     */
    AdminDTO getAdminById(Long id);

    /**
     * 搜索管理员
     */
    List<AdminDTO> searchAdmins(String keyword);

    /**
     * 根据角色获取管理员列表
     */
    List<AdminDTO> getAdminsByRole(String role);

    /**
     * 创建管理员
     */
    AdminDTO createAdmin(AdminCreateRequest request);

    /**
     * 更新管理员
     */
    AdminDTO updateAdmin(Long id, AdminUpdateRequest request);

    /**
     * 删除管理员
     */
    void deleteAdmin(Long id);

    /**
     * 启用/禁用管理员
     */
    AdminDTO toggleAdminStatus(Long id);

    /**
     * 验证管理员登录
     */
    boolean validateLogin(String username, String password);

    /**
     * 更新最后登录时间
     */
    void updateLastLoginTime(String username);

    /**
     * 将Admin实体转换为AdminDTO
     */
    AdminDTO convertToDTO(Admin admin);

    /**
     * 将Admin实体列表转换为AdminDTO列表
     */
    List<AdminDTO> convertToDTOList(List<Admin> admins);
}
