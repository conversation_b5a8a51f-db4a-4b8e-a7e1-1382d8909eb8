# 员工管理字段映射说明

## 字段映射关系

由于历史原因，员工管理系统中的某些字段实际存储的内容与字段名称不符。以下是详细的字段映射关系：

### 数据库字段 -> 实际存储内容

| 数据库字段名 | 实际存储内容 | 前端显示标签 | 说明 |
|-------------|-------------|-------------|-----|
| `avatar` | 移动电话号码 | 移动电话 | 原本用于存储头像URL，现在存储移动电话号码 |
| `employee_id` | 传真号码 | 传真 | 原本用于存储员工工号，现在存储传真号码 |
| `manager` | 邮政编码 | 邮政编码 | 原本用于存储上级领导信息，现在存储邮政编码 |

### 正常字段（无映射）

以下字段按照正常的含义使用：

- `username` - 用户名
- `name` - 姓名
- `department_id` - 部门ID（关联departments表）
- `department` - 部门名称（冗余字段）
- `position` - 职位
- `email` - 邮箱
- `phone` - 电话号码
- `password` - 密码
- `bio` - 个人简介
- `enabled` - 启用状态

## 前端实现

### 表单验证规则

```javascript
const rules = {
  // 传真号码验证
  employeeId: [
    { required: true, message: '请输入传真号码', trigger: 'blur' },
    { pattern: /^[\d-]+$/, message: '请输入正确的传真号码格式', trigger: 'blur' }
  ],
  
  // 移动电话验证
  avatar: [
    { required: true, message: '请输入移动电话号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的移动电话号码格式', trigger: 'blur' }
  ],
  
  // 邮政编码验证
  manager: [
    { pattern: /^\d{6}$/, message: '请输入正确的邮政编码格式（6位数字）', trigger: 'blur' }
  ]
}
```

### 表格列配置

```javascript
// 员工列表表格列
<el-table-column prop="employeeId" label="传真" width="120" />
<el-table-column prop="avatar" label="移动电话" width="130" />
<el-table-column prop="manager" label="邮政编码" width="100" />
```

## 注意事项

1. **数据迁移**: 如果需要修正字段映射关系，需要进行数据迁移
2. **API接口**: 后端API接口需要保持与前端字段映射的一致性
3. **文档更新**: 修改字段映射时，需要同步更新此文档
4. **测试**: 修改字段映射后，需要进行完整的功能测试

## 修改历史

- 2024-01-XX: 初始创建，根据实际业务需求调整字段映射关系
- 移除了头像上传功能，将avatar字段用于存储移动电话号码
- 将employee_id字段用于存储传真号码
- 将manager字段用于存储邮政编码

## 相关文件

### 前端文件
- `src/views/EmployeeManagement.vue` - 员工管理页面
- `src/components/EmployeeForm.vue` - 员工表单组件

### 后端文件
- `src/main/java/com/minmetals/entity/Employee.java` - 员工实体类
- `src/main/java/com/minmetals/controller/EmployeeController.java` - 员工控制器
- `src/main/java/com/minmetals/service/impl/EmployeeServiceImpl.java` - 员工服务实现
