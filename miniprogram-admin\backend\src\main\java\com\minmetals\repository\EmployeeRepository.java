package com.minmetals.repository;

import com.minmetals.entity.Employee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 员工数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface EmployeeRepository extends JpaRepository<Employee, Long> {

    /**
     * 根据用户名查找员工
     */
    Optional<Employee> findByUsername(String username);

    /**
     * 根据员工工号查找员工
     */
    Optional<Employee> findByEmployeeId(String employeeId);

    /**
     * 根据用户名或员工工号查找员工
     */
    Optional<Employee> findByUsernameOrEmployeeId(String username, String employeeId);

    /**
     * 查找所有启用的员工
     */
    List<Employee> findByEnabledTrueOrderByCreatedAtDesc();

    /**
     * 根据部门查找员工
     */
    List<Employee> findByDepartmentAndEnabledTrueOrderByCreatedAtDesc(String department);

    /**
     * 根据职位查找员工
     */
    List<Employee> findByPositionAndEnabledTrueOrderByCreatedAtDesc(String position);

    /**
     * 模糊搜索员工（姓名、工号、部门、职位）
     */
    @Query("SELECT e FROM Employee e WHERE e.enabled = true AND " +
           "(LOWER(e.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(e.employeeId) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(e.department) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(e.position) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY e.createdAt DESC")
    List<Employee> searchEmployees(@Param("keyword") String keyword);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查员工工号是否存在
     */
    boolean existsByEmployeeId(String employeeId);

    /**
     * 检查用户名是否存在（排除指定ID）
     */
    boolean existsByUsernameAndIdNot(String username, Long id);

    /**
     * 检查员工工号是否存在（排除指定ID）
     */
    boolean existsByEmployeeIdAndIdNot(String employeeId, Long id);

    /**
     * 根据部门ID统计员工数量
     */
    long countByDepartmentId(Long departmentId);

    /**
     * 根据部门ID查询员工列表
     */
    List<Employee> findByDepartmentId(Long departmentId);

    /**
     * 根据部门ID查询启用状态的员工列表
     */
    List<Employee> findByDepartmentIdAndEnabledTrue(Long departmentId);
}
