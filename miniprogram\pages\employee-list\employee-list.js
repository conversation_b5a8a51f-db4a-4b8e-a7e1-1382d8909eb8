// pages/employee-list/employee-list.js
const { api } = require('../../utils/api')

Page({
  data: {
    userInfo: {},
    employees: [],
    filteredEmployees: [],
    departments: [],
    positions: [],
    departmentOptions: ['全部部门'],
    positionOptions: ['全部职位'],
    selectedDepartment: '',
    selectedPosition: '',
    selectedDepartmentIndex: 0,
    selectedPositionIndex: 0,
    loading: true
  },

  onLoad() {
    this.checkLoginStatus()
    this.loadUserInfo()
    this.loadEmployeeList()
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadEmployeeList()
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp()
    if (!app.globalData.isLoggedIn) {
      wx.redirectTo({
        url: '/pages/login/login'
      })
      return
    }
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const app = getApp()
    if (app.globalData.employeeInfo) {
      this.setData({
        userInfo: app.globalData.employeeInfo
      })
    }
  },

  /**
   * 加载员工列表
   */
  async loadEmployeeList() {
    try {
      this.setData({
        loading: true
      })

      wx.showLoading({
        title: '加载中...'
      })

      const result = await api.getEmployeeList()

      if (result.code === 200 && result.data) {
        const employees = result.data
        this.setData({
          employees,
          filteredEmployees: employees,
          loading: false
        })

        // 提取部门和职位列表
        this.extractDepartmentsAndPositions(employees)
      } else {
        throw new Error(result.message || '获取员工列表失败')
      }

    } catch (error) {
      console.error('加载员工列表失败:', error)

      this.setData({
        loading: false,
        employees: [],
        filteredEmployees: []
      })

      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none',
        duration: 3000
      })

    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 提取部门和职位列表
   */
  extractDepartmentsAndPositions(employees) {
    const departments = new Set()
    const positions = new Set()

    employees.forEach(employee => {
      if (employee.department) {
        departments.add(employee.department)
      }
      if (employee.position) {
        positions.add(employee.position)
      }
    })

    const departmentOptions = ['全部部门', ...Array.from(departments).sort()]
    const positionOptions = ['全部职位', ...Array.from(positions).sort()]

    this.setData({
      departments: Array.from(departments),
      positions: Array.from(positions),
      departmentOptions,
      positionOptions
    })
  },

  /**
   * 部门选择变化
   */
  onDepartmentChange(e) {
    const index = parseInt(e.detail.value)
    const selectedDepartment = index === 0 ? '' : this.data.departmentOptions[index]

    this.setData({
      selectedDepartmentIndex: index,
      selectedDepartment
    })

    this.filterEmployees()
  },

  /**
   * 职位选择变化
   */
  onPositionChange(e) {
    const index = parseInt(e.detail.value)
    const selectedPosition = index === 0 ? '' : this.data.positionOptions[index]

    this.setData({
      selectedPositionIndex: index,
      selectedPosition
    })

    this.filterEmployees()
  },

  /**
   * 清除筛选条件
   */
  clearFilters() {
    this.setData({
      selectedDepartment: '',
      selectedPosition: '',
      selectedDepartmentIndex: 0,
      selectedPositionIndex: 0
    })

    this.filterEmployees()
  },

  /**
   * 过滤员工列表
   */
  filterEmployees() {
    const { employees, selectedDepartment, selectedPosition } = this.data

    let filteredEmployees = employees

    // 按部门筛选
    if (selectedDepartment) {
      filteredEmployees = filteredEmployees.filter(employee =>
        employee.department === selectedDepartment
      )
    }

    // 按职位筛选
    if (selectedPosition) {
      filteredEmployees = filteredEmployees.filter(employee =>
        employee.position === selectedPosition
      )
    }

    this.setData({
      filteredEmployees
    })
  },

  /**
   * 跳转到员工详情页
   */
  goToDetail(e) {
    const employee = e.currentTarget.dataset.employee

    if (!employee || !employee.id) {
      wx.showToast({
        title: '员工信息错误',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/employee-detail/employee-detail?id=${employee.id}`
    })
  },

  /**
   * 处理登出
   */
  handleLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          const app = getApp()
          app.logout()

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })

          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/index/index'
            })
          }, 1500)
        }
      }
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadEmployeeList().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: '中国五矿 - 员工名录',
      path: '/pages/index/index'
    }
  }
})
