package com.minmetals.service;

import com.minmetals.dto.EmployeeCreateRequest;
import com.minmetals.dto.EmployeeDTO;
import com.minmetals.dto.EmployeeUpdateRequest;
import com.minmetals.entity.Employee;

import java.util.List;

/**
 * 员工服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface EmployeeService {

    /**
     * 根据用户名查找员工
     */
    Employee findByUsername(String username);

    /**
     * 根据ID查找员工
     */
    Employee findById(Long id);

    /**
     * 获取所有启用的员工列表
     */
    List<EmployeeDTO> getAllEmployees();

    /**
     * 根据ID获取员工详情
     */
    EmployeeDTO getEmployeeById(Long id);



    /**
     * 搜索员工
     */
    List<EmployeeDTO> searchEmployees(String keyword);

    /**
     * 根据部门获取员工列表
     */
    List<EmployeeDTO> getEmployeesByDepartment(String department);

    /**
     * 根据职位获取员工列表
     */
    List<EmployeeDTO> getEmployeesByPosition(String position);

    /**
     * 创建员工
     */
    EmployeeDTO createEmployee(EmployeeCreateRequest request);

    /**
     * 更新员工
     */
    EmployeeDTO updateEmployee(Long id, EmployeeUpdateRequest request);

    /**
     * 删除员工
     */
    void deleteEmployee(Long id);

    /**
     * 启用/禁用员工
     */
    EmployeeDTO toggleEmployeeStatus(Long id);

    /**
     * 将Employee实体转换为EmployeeDTO
     */
    EmployeeDTO convertToDTO(Employee employee);

    /**
     * 将Employee实体列表转换为EmployeeDTO列表
     */
    List<EmployeeDTO> convertToDTOList(List<Employee> employees);

    /**
     * 根据部门ID获取员工列表
     */
    List<EmployeeDTO> getEmployeesByDepartmentId(Long departmentId);

    /**
     * 根据部门ID获取启用状态的员工列表
     */
    List<EmployeeDTO> getEnabledEmployeesByDepartmentId(Long departmentId);
}
