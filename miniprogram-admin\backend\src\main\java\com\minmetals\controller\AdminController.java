package com.minmetals.controller;

import com.minmetals.dto.*;
import com.minmetals.service.AdminService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 管理员管理控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
@Tag(name = "管理员管理", description = "管理员增删改查相关接口")
public class AdminController {

    private final AdminService adminService;

    /**
     * 获取所有管理员列表
     */
    @GetMapping
    @Operation(summary = "获取所有管理员列表", description = "获取所有启用的管理员列表")
    public ApiResponse<List<AdminDTO>> getAllAdmins() {
        log.info("获取所有管理员列表");
        try {
            List<AdminDTO> admins = adminService.getAllAdmins();
            return ApiResponse.success("获取成功", admins);
        } catch (Exception e) {
            log.error("获取管理员列表失败", e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取管理员详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取管理员详情", description = "根据ID获取管理员详情")
    public ApiResponse<AdminDTO> getAdminById(
            @Parameter(description = "管理员ID") @PathVariable Long id) {
        log.info("获取管理员详情，ID: {}", id);
        try {
            AdminDTO admin = adminService.getAdminById(id);
            return ApiResponse.success("获取成功", admin);
        } catch (Exception e) {
            log.error("获取管理员详情失败，ID: {}", id, e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 搜索管理员
     */
    @GetMapping("/search")
    @Operation(summary = "搜索管理员", description = "根据关键词搜索管理员")
    public ApiResponse<List<AdminDTO>> searchAdmins(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        log.info("搜索管理员，关键词: {}", keyword);
        try {
            List<AdminDTO> admins = adminService.searchAdmins(keyword);
            return ApiResponse.success("搜索成功", admins);
        } catch (Exception e) {
            log.error("搜索管理员失败，关键词: {}", keyword, e);
            return ApiResponse.error("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 根据角色获取管理员列表
     */
    @GetMapping("/role/{role}")
    @Operation(summary = "根据角色获取管理员列表", description = "根据角色获取管理员列表")
    public ApiResponse<List<AdminDTO>> getAdminsByRole(
            @Parameter(description = "角色") @PathVariable String role) {
        log.info("根据角色获取管理员列表，角色: {}", role);
        try {
            List<AdminDTO> admins = adminService.getAdminsByRole(role);
            return ApiResponse.success("获取成功", admins);
        } catch (Exception e) {
            log.error("根据角色获取管理员列表失败，角色: {}", role, e);
            return ApiResponse.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 创建管理员
     */
    @PostMapping
    @Operation(summary = "创建管理员", description = "创建新的管理员")
    public ApiResponse<AdminDTO> createAdmin(@Valid @RequestBody AdminCreateRequest request) {
        log.info("创建管理员，用户名: {}", request.getUsername());
        try {
            AdminDTO admin = adminService.createAdmin(request);
            return ApiResponse.success("创建成功", admin);
        } catch (Exception e) {
            log.error("创建管理员失败，用户名: {}", request.getUsername(), e);
            return ApiResponse.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新管理员
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新管理员", description = "更新管理员信息")
    public ApiResponse<AdminDTO> updateAdmin(
            @Parameter(description = "管理员ID") @PathVariable Long id,
            @Valid @RequestBody AdminUpdateRequest request) {
        log.info("更新管理员，ID: {}", id);
        try {
            AdminDTO admin = adminService.updateAdmin(id, request);
            return ApiResponse.success("更新成功", admin);
        } catch (Exception e) {
            log.error("更新管理员失败，ID: {}", id, e);
            return ApiResponse.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除管理员
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除管理员", description = "删除管理员")
    public ApiResponse<String> deleteAdmin(
            @Parameter(description = "管理员ID") @PathVariable Long id) {
        log.info("删除管理员，ID: {}", id);
        try {
            adminService.deleteAdmin(id);
            return ApiResponse.success("删除成功", "管理员已删除");
        } catch (Exception e) {
            log.error("删除管理员失败，ID: {}", id, e);
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用管理员
     */
    @PatchMapping("/{id}/toggle-status")
    @Operation(summary = "启用/禁用管理员", description = "切换管理员的启用状态")
    public ApiResponse<AdminDTO> toggleAdminStatus(
            @Parameter(description = "管理员ID") @PathVariable Long id) {
        log.info("切换管理员状态，ID: {}", id);
        try {
            AdminDTO admin = adminService.toggleAdminStatus(id);
            return ApiResponse.success("状态切换成功", admin);
        } catch (Exception e) {
            log.error("切换管理员状态失败，ID: {}", id, e);
            return ApiResponse.error("状态切换失败: " + e.getMessage());
        }
    }
}
