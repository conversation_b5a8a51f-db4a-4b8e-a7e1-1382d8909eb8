-- 初始化数据脚本
-- 注意：密码使用明文存储，所有用户的密码都是 "123456"

-- 插入管理员数据（如果不存在）
INSERT INTO admins (name, username, password, email, phone, role, enabled, description, created_at, updated_at)
SELECT '超级管理员', 'superadmin', '123456', '<EMAIL>', '86-10-68494100', 'SUPER_ADMIN', true, '系统超级管理员，拥有所有权限', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM admins WHERE username = 'superadmin');

INSERT INTO admins (name, username, password, email, phone, role, enabled, description, created_at, updated_at)
SELECT '系统管理员', 'admin', '123456', '<EMAIL>', '86-10-68494101', 'ADMIN', true, '系统管理员，负责日常管理工作', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM admins WHERE username = 'admin');

INSERT INTO admins (name, username, password, email, phone, role, enabled, description, created_at, updated_at)
SELECT '人事管理员', 'hradmin', '123456', '<EMAIL>', '86-10-68494102', 'ADMIN', true, '人事管理员，负责员工管理', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM admins WHERE username = 'hradmin');

-- 插入员工数据
INSERT INTO employees (name, employee_id, username, password, department, position, level, phone, email, address, manager, work_location, description, hire_date, enabled, created_at, updated_at) VALUES
('李韬', 'EMP001', 'litao', '123456', '制造业事业部', '业务经理', '高级经理', '86-10-68494121', '<EMAIL>', '北京市海淀区三里河路5号五矿大厦', '张总', '北京总部', '负责制造业务的整体规划和管理', '2020-01-15', true, NOW(), NOW()),
('张明', 'EMP002', 'zhangming', '123456', '人力资源部', '人事专员', '专员', '86-10-68494122', '<EMAIL>', '北京市海淀区三里河路5号五矿大厦', '李韬', '北京总部', '负责员工招聘和培训工作', '2021-03-20', true, NOW(), NOW()),
('王丽', 'EMP003', 'wangli', '123456', '财务部', '财务经理', '经理', '86-10-68494123', '<EMAIL>', '北京市海淀区三里河路5号五矿大厦', '李韬', '北京总部', '负责公司财务管理和预算控制', '2019-08-10', true, NOW(), NOW()),
('刘强', 'EMP004', 'liuqiang', '123456', '技术部', '技术总监', '总监', '86-10-68494124', '<EMAIL>', '北京市海淀区三里河路5号五矿大厦', '李韬', '北京总部', '负责技术研发和创新管理', '2018-05-15', true, NOW(), NOW()),
('陈静', 'EMP005', 'chenjing', '123456', '市场部', '市场专员', '专员', '86-10-68494125', '<EMAIL>', '北京市海淀区三里河路5号五矿大厦', '王丽', '北京总部', '负责市场调研和品牌推广', '2022-01-10', true, NOW(), NOW()),
('赵伟', 'EMP006', 'zhaowei', '123456', '销售部', '销售经理', '经理', '86-10-68494126', '<EMAIL>', '北京市海淀区三里河路5号五矿大厦', '刘强', '北京总部', '负责客户关系维护和销售业绩管理', '2020-11-05', true, NOW(), NOW()),
('孙娜', 'EMP007', 'sunna', '123456', '行政部', '行政助理', '助理', '86-10-68494127', '<EMAIL>', '北京市海淀区三里河路5号五矿大厦', '陈静', '北京总部', '负责日常行政事务和办公室管理', '2023-02-20', true, NOW(), NOW()),
('周杰', 'EMP008', 'zhoujie', '123456', '法务部', '法务顾问', '顾问', '86-10-68494128', '<EMAIL>', '北京市海淀区三里河路5号五矿大厦', '赵伟', '北京总部', '负责合同审查和法律风险控制', '2021-07-12', true, NOW(), NOW());

-- 插入背景图片数据
INSERT INTO background_images (name, image_url, description, enabled, sort_order, created_at, updated_at) VALUES
('公司大楼', 'https://example.com/images/company-building.jpg', '中国五矿总部大楼外景', true, 1, NOW(), NOW()),
('企业文化', 'https://example.com/images/company-culture.jpg', '展示企业文化和团队精神', true, 2, NOW(), NOW()),
('产业布局', 'https://example.com/images/industry-layout.jpg', '五矿集团产业布局展示', true, 3, NOW(), NOW());
