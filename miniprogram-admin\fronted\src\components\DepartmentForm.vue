<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑部门' : '新增部门'"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="部门名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入部门名称"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门级别" prop="level">
            <el-select
              v-model="form.level"
              placeholder="请选择级别"
              style="width: 100%"
              :disabled="isEdit || !!parentDepartment"
              @change="handleLevelChange"
            >
              <el-option label="一级部门" :value="1" />
              <el-option label="二级部门" :value="2" />
              <el-option label="三级部门" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上级部门" prop="parentId">
            <el-select
              v-model="form.parentId"
              placeholder="请选择上级部门"
              style="width: 100%"
              :disabled="!!parentDepartment || form.level === 1"
              clearable
              @change="handleParentChange"
            >
              <el-option
                v-for="dept in availableParentDepartments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="状态">
        <el-switch
          v-model="form.enabled"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ loading ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const props = defineProps({
  visible: Boolean,
  department: Object,
  parentDepartment: Object,
  isEdit: Boolean
})

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref()
const loading = ref(false)
const allDepartments = ref([])

const form = reactive({
  name: '',
  parentId: null,
  level: 1,
  enabled: true
})

// 可用的上级部门
const availableParentDepartments = computed(() => {
  if (form.level === 1) {
    return [] // 一级部门没有上级部门
  }

  if (props.isEdit) {
    // 编辑时，排除自己和自己的子部门
    return allDepartments.value.filter(dept =>
      dept.id !== props.department?.id &&
      dept.level === form.level - 1
    )
  }

  // 新增时，根据选择的级别过滤
  const maxLevel = form.level - 1
  return allDepartments.value.filter(dept => dept.level === maxLevel)
})

const rules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择部门级别', trigger: 'change' }
  ]
}

// 处理级别变化
const handleLevelChange = (level) => {
  if (level === 1) {
    form.parentId = null
  }
}

// 处理父部门变化
const handleParentChange = (parentId) => {
  // 父部门变化时不需要特殊处理，级别已经固定
}

// 加载所有部门
const loadAllDepartments = async () => {
  try {
    const response = await request.get('/departments')
    allDepartments.value = response.data || []
  } catch (error) {
    console.error('加载部门列表失败:', error)
  }
}

// 监听部门数据变化
watch(() => props.department, (newDepartment) => {
  if (newDepartment) {
    Object.assign(form, {
      name: newDepartment.name || '',
      parentId: newDepartment.parentId || null,
      level: newDepartment.level || 1,
      enabled: newDepartment.enabled !== false
    })
  } else {
    // 重置表单
    Object.assign(form, {
      name: '',
      parentId: null,
      level: 1,
      enabled: true
    })
  }
}, { immediate: true })

// 监听父部门变化
watch(() => props.parentDepartment, (newParent) => {
  if (newParent) {
    form.parentId = newParent.id
    form.level = newParent.level + 1
  }
}, { immediate: true })

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    loadAllDepartments()
  }
})

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const submitData = { ...form }

    if (props.isEdit) {
      await request.put(`/departments/${props.department.id}`, submitData)
      ElMessage.success('更新成功')
    } else {
      await request.post('/departments', submitData)
      ElMessage.success('创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('保存部门失败:', error)
    ElMessage.error('保存失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
