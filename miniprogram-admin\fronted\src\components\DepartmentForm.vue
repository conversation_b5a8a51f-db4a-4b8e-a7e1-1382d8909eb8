<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑部门' : '新增部门'"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入部门名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门编码" prop="code">
            <el-input
              v-model="form.code"
              :disabled="isEdit"
              placeholder="请输入部门编码"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上级部门" prop="parentId">
            <el-select
              v-model="form.parentId"
              placeholder="请选择上级部门"
              style="width: 100%"
              :disabled="!!parentDepartment"
              clearable
              @change="handleParentChange"
            >
              <el-option
                v-for="dept in availableParentDepartments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门级别" prop="level">
            <el-select
              v-model="form.level"
              placeholder="请选择级别"
              style="width: 100%"
              :disabled="isEdit || !!parentDepartment"
            >
              <el-option
                v-for="level in availableLevels"
                :key="level.value"
                :label="level.label"
                :value="level.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人" prop="manager">
            <el-input
              v-model="form.manager"
              placeholder="请输入负责人"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input
              v-model="form.phone"
              placeholder="请输入联系电话"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系邮箱" prop="email">
            <el-input
              v-model="form.email"
              placeholder="请输入联系邮箱"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序号" prop="sortOrder">
            <el-input-number
              v-model="form.sortOrder"
              :min="0"
              :max="999"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="办公地址" prop="address">
        <el-input
          v-model="form.address"
          placeholder="请输入办公地址"
        />
      </el-form-item>

      <el-form-item label="部门描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入部门描述"
        />
      </el-form-item>

      <el-form-item label="状态">
        <el-switch
          v-model="form.enabled"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ loading ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const props = defineProps({
  visible: Boolean,
  department: Object,
  parentDepartment: Object,
  isEdit: Boolean
})

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref()
const loading = ref(false)
const allDepartments = ref([])

const form = reactive({
  name: '',
  code: '',
  parentId: null,
  level: 1,
  description: '',
  manager: '',
  phone: '',
  email: '',
  address: '',
  sortOrder: 0,
  enabled: true
})

// 可用的上级部门
const availableParentDepartments = computed(() => {
  if (props.isEdit) {
    // 编辑时，排除自己和自己的子部门
    return allDepartments.value.filter(dept => 
      dept.id !== props.department?.id && 
      dept.level < (props.department?.level || 3)
    )
  }
  
  // 新增时，根据选择的级别过滤
  const maxLevel = form.level - 1
  return allDepartments.value.filter(dept => dept.level === maxLevel)
})

// 可用的级别选项
const availableLevels = computed(() => {
  if (props.parentDepartment) {
    // 如果指定了父部门，级别自动为父部门级别+1
    const level = props.parentDepartment.level + 1
    return [{ label: `${getLevelText(level)}部门`, value: level }]
  }
  
  if (form.parentId) {
    // 如果选择了父部门，级别为父部门级别+1
    const parent = allDepartments.value.find(d => d.id === form.parentId)
    if (parent) {
      const level = parent.level + 1
      return [{ label: `${getLevelText(level)}部门`, value: level }]
    }
  }
  
  // 否则显示所有可用级别
  return [
    { label: '一级部门', value: 1 },
    { label: '二级部门', value: 2 },
    { label: '三级部门', value: 3 }
  ]
})

const rules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入部门编码', trigger: 'blur' },
    { min: 2, max: 20, message: '部门编码长度在 2 到 20 个字符', trigger: 'blur' },
    { validator: validateCode, trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择部门级别', trigger: 'change' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 验证部门编码唯一性
async function validateCode(rule, value, callback) {
  if (!value) {
    callback()
    return
  }
  
  try {
    const excludeId = props.isEdit ? props.department.id : null
    const response = await request.get('/departments/code/check', {
      params: { code: value, excludeId }
    })
    
    if (!response.data) {
      callback(new Error('部门编码已存在'))
    } else {
      callback()
    }
  } catch (error) {
    callback(new Error('验证部门编码失败'))
  }
}

// 处理父部门变化
const handleParentChange = (parentId) => {
  if (parentId) {
    const parent = allDepartments.value.find(d => d.id === parentId)
    if (parent) {
      form.level = parent.level + 1
    }
  } else {
    form.level = 1
  }
}

// 获取级别文本
const getLevelText = (level) => {
  const texts = { 1: '一级', 2: '二级', 3: '三级' }
  return texts[level] || ''
}

// 加载所有部门
const loadAllDepartments = async () => {
  try {
    const response = await request.get('/departments')
    allDepartments.value = response.data || []
  } catch (error) {
    console.error('加载部门列表失败:', error)
  }
}

// 监听部门数据变化
watch(() => props.department, (newDepartment) => {
  if (newDepartment) {
    Object.assign(form, {
      name: newDepartment.name || '',
      code: newDepartment.code || '',
      parentId: newDepartment.parentId || null,
      level: newDepartment.level || 1,
      description: newDepartment.description || '',
      manager: newDepartment.manager || '',
      phone: newDepartment.phone || '',
      email: newDepartment.email || '',
      address: newDepartment.address || '',
      sortOrder: newDepartment.sortOrder || 0,
      enabled: newDepartment.enabled !== false
    })
  } else {
    // 重置表单
    Object.assign(form, {
      name: '',
      code: '',
      parentId: null,
      level: 1,
      description: '',
      manager: '',
      phone: '',
      email: '',
      address: '',
      sortOrder: 0,
      enabled: true
    })
  }
}, { immediate: true })

// 监听父部门变化
watch(() => props.parentDepartment, (newParent) => {
  if (newParent) {
    form.parentId = newParent.id
    form.level = newParent.level + 1
  }
}, { immediate: true })

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    loadAllDepartments()
  }
})

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const submitData = { ...form }

    if (props.isEdit) {
      await request.put(`/departments/${props.department.id}`, submitData)
      ElMessage.success('更新成功')
    } else {
      await request.post('/departments', submitData)
      ElMessage.success('创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('保存部门失败:', error)
    ElMessage.error('保存失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
