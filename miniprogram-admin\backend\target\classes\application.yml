# Spring Boot 配置文件
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: company-intro-api

  # 数据库配置 - VastBase
  datasource:
    url: ****************************************************
    username: root
    password: jyptWXdid#
    driver-class-name: org.postgresql.Driver

    # 连接池配置
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
    open-in-view: false

  # 数据初始化配置
  sql:
    init:
      mode: never
      data-locations: classpath:admin-data.sql
      continue-on-error: true

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# 自定义文件上传配置
file:
  upload:
    path: uploads  # 文件上传目录
    max-size: 10485760  # 最大文件大小 (10MB)

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# 日志配置
logging:
  level:
    com.minmetals: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/company-intro-api.log

# 自定义配置
app:
  # JWT配置
  jwt:
    secret: minmetals-company-intro-secret-key-2024-very-long-secure-key-for-hs512-algorithm-must-be-at-least-512-bits-long
    expiration: 86400000 # 24小时，单位：毫秒

  # 文件存储配置
  file:
    upload-dir: uploads/
    max-size: 10485760 # 10MB

  # 跨域配置
  cors:
    allowed-origins: "*"
    allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
    allowed-headers: "*"
    allow-credentials: true

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized
