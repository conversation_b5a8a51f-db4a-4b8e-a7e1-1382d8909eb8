import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/store/auth'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/employees',
    name: 'EmployeeManagement',
    component: () => import('@/views/EmployeeManagement.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/departments',
    name: 'DepartmentManagement',
    component: () => import('@/views/DepartmentManagement.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/background-images',
    name: 'BackgroundImageManagement',
    component: () => import('@/views/BackgroundImageManagement.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/long-images',
    name: 'LongImageManagement',
    component: () => import('@/views/LongImageManagement.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  console.log(`路由守卫: 从 ${from.path} 到 ${to.path}`)
  console.log('当前登录状态:', authStore.isLoggedIn)
  console.log('当前token:', authStore.token)

  // 如果路由需要认证
  if (to.meta.requiresAuth) {
    console.log('路由需要认证')
    if (!authStore.isLoggedIn) {
      console.log('未登录，跳转到登录页')
      // 未登录，跳转到登录页
      next('/login')
      return
    }

    // 验证token有效性
    console.log('验证token有效性...')
    const isValid = await authStore.validateToken()
    console.log('token验证结果:', isValid)
    if (!isValid) {
      console.log('token无效，跳转到登录页')
      next('/login')
      return
    }
  }

  // 如果已登录且访问登录页，跳转到仪表板
  if (to.path === '/login' && authStore.isLoggedIn) {
    console.log('已登录用户访问登录页，跳转到仪表板')
    next('/dashboard')
    return
  }

  console.log('路由守卫通过，继续导航')
  next()
})

export default router
