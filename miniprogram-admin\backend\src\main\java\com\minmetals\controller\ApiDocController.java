package com.minmetals.controller;

import com.minmetals.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * API文档控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@Tag(name = "API文档", description = "API文档相关接口")
public class ApiDocController {

    /**
     * 根路径重定向到Swagger UI
     */
    @GetMapping("/")
    public void redirectToSwagger(HttpServletResponse response) throws IOException {
        response.sendRedirect("/swagger-ui/index.html");
    }

    /**
     * API信息
     */
    @GetMapping("/api")
    @Operation(summary = "获取API信息", description = "获取API基本信息和主要端点")
    public ApiResponse<Map<String, Object>> getApiInfo() {
        log.info("获取API信息");

        Map<String, Object> apiInfo = new HashMap<>();
        apiInfo.put("name", "中国五矿公司介绍小程序后端API");
        apiInfo.put("version", "1.0.0");
        apiInfo.put("description", "提供员工信息管理、背景图片管理、用户认证等功能的RESTful API");
        apiInfo.put("swagger_url", "/swagger-ui/index.html");
        apiInfo.put("api_docs_url", "/v3/api-docs");

        Map<String, String> endpoints = new HashMap<>();
        endpoints.put("登录", "POST /auth/login");
        endpoints.put("员工列表", "GET /employees");
        endpoints.put("员工详情", "GET /employees/{id}");
        endpoints.put("背景图片", "GET /background/image");
        endpoints.put("健康检查", "GET /actuator/health");

        apiInfo.put("main_endpoints", endpoints);

        return ApiResponse.success("API信息获取成功", apiInfo);
    }
}
