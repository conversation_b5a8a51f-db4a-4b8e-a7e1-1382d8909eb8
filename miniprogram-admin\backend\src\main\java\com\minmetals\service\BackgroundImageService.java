package com.minmetals.service;

import com.minmetals.dto.BackgroundImageCreateRequest;
import com.minmetals.dto.BackgroundImageUpdateRequest;
import com.minmetals.entity.BackgroundImage;

import java.util.List;

/**
 * 背景图片服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface BackgroundImageService {

    /**
     * 获取当前背景图片
     */
    BackgroundImage getCurrentBackgroundImage();

    /**
     * 获取所有启用的背景图片
     */
    List<BackgroundImage> getAllEnabledBackgroundImages();

    /**
     * 根据ID获取背景图片
     */
    BackgroundImage getBackgroundImageById(Long id);

    /**
     * 获取所有背景图片（包括禁用的）
     */
    List<BackgroundImage> getAllBackgroundImages();

    /**
     * 创建背景图片
     */
    BackgroundImage createBackgroundImage(BackgroundImageCreateRequest request);

    /**
     * 更新背景图片
     */
    BackgroundImage updateBackgroundImage(Long id, BackgroundImageUpdateRequest request);

    /**
     * 删除背景图片
     */
    void deleteBackgroundImage(Long id);

    /**
     * 启用/禁用背景图片
     */
    BackgroundImage toggleBackgroundImageStatus(Long id);

    /**
     * 搜索背景图片
     */
    List<BackgroundImage> searchBackgroundImages(String keyword);
}
