package com.minmetals.controller;

import com.minmetals.dto.ApiResponse;
import com.minmetals.dto.LoginRequest;
import com.minmetals.dto.LoginResponse;
import com.minmetals.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 认证控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Validated
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    private final AuthService authService;

    /**
     * 员工登录
     */
    @PostMapping("/login")
    @Operation(summary = "员工登录", description = "使用用户名和密码进行登录认证")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        log.info("收到登录请求，用户名: {}", loginRequest.getUsername());

        try {
            LoginResponse loginResponse = authService.login(loginRequest);
            return ApiResponse.success("登录成功", loginResponse);
        } catch (RuntimeException e) {
            log.warn("登录失败: {}", e.getMessage());
            return ApiResponse.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("登录时发生异常", e);
            return ApiResponse.error("登录失败，请稍后重试");
        }
    }

    /**
     * 验证JWT令牌
     */
    @GetMapping("/validate")
    @Operation(summary = "验证JWT令牌", description = "验证JWT令牌的有效性")
    public ApiResponse<String> validateToken(HttpServletRequest request) {
        String token = extractTokenFromRequest(request);

        if (token == null) {
            return ApiResponse.unauthorized("缺少认证令牌");
        }

        try {
            boolean isValid = authService.validateToken(token);
            if (isValid) {
                String username = authService.getUsernameFromToken(token);
                return ApiResponse.success("令牌有效", username);
            } else {
                return ApiResponse.unauthorized("令牌无效或已过期");
            }
        } catch (Exception e) {
            log.error("验证令牌时发生异常", e);
            return ApiResponse.unauthorized("令牌验证失败");
        }
    }



    /**
     * 从请求中提取JWT令牌
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
