package com.minmetals.service.impl;

import com.minmetals.dto.EmployeeDTO;
import com.minmetals.dto.LoginRequest;
import com.minmetals.dto.LoginResponse;
import com.minmetals.entity.Employee;
import com.minmetals.service.AuthService;
import com.minmetals.service.EmployeeService;
import com.minmetals.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 认证服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class AuthServiceImpl implements AuthService {

    private final EmployeeService employeeService;
    private final JwtUtil jwtUtil;

    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        log.info("用户 {} 尝试登录", loginRequest.getUsername());

        try {
            // 查找员工
            Employee employee = employeeService.findByUsername(loginRequest.getUsername());

            // 检查员工是否启用
            if (!employee.getEnabled()) {
                throw new RuntimeException("账户已被禁用，请联系管理员");
            }

            // 验证密码（明文比较）
            if (!employee.getPassword().equals(loginRequest.getPassword())) {
                throw new RuntimeException("用户名或密码错误");
            }

            // 生成JWT令牌
            String token = jwtUtil.generateToken(employee.getUsername(), employee.getId(), "EMPLOYEE");

            // 转换员工信息为DTO
            EmployeeDTO employeeDTO = employeeService.convertToDTO(employee);

            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setToken(token);
            response.setUsername(employee.getUsername());
            response.setName(employee.getName());
            response.setRole(employee.getLevel()); // 使用级别作为角色
            response.setUserType("EMPLOYEE");
            response.setEmployeeInfo(employeeDTO);

            log.info("用户 {} 登录成功", loginRequest.getUsername());
            return response;

        } catch (RuntimeException e) {
            log.warn("用户 {} 登录失败: {}", loginRequest.getUsername(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("用户 {} 登录时发生异常", loginRequest.getUsername(), e);
            throw new RuntimeException("登录失败，请稍后重试");
        }
    }

    @Override
    public boolean validateToken(String token) {
        try {
            if (token == null || token.trim().isEmpty()) {
                return false;
            }

            // 检查令牌格式
            if (!jwtUtil.isValidTokenFormat(token)) {
                return false;
            }

            // 获取用户名并验证
            String username = jwtUtil.getUsernameFromToken(token);
            if (username == null || username.trim().isEmpty()) {
                return false;
            }

            // 验证令牌
            return jwtUtil.validateToken(token, username);

        } catch (Exception e) {
            log.debug("验证JWT令牌失败: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public String getUsernameFromToken(String token) {
        try {
            return jwtUtil.getUsernameFromToken(token);
        } catch (Exception e) {
            log.error("从JWT令牌获取用户名失败: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public Long getUserIdFromToken(String token) {
        try {
            return jwtUtil.getUserIdFromToken(token);
        } catch (Exception e) {
            log.error("从JWT令牌获取用户ID失败: {}", e.getMessage());
            return null;
        }
    }
}
