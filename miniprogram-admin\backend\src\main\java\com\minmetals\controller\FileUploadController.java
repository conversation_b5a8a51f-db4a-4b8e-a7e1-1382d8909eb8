package com.minmetals.controller;

import com.minmetals.dto.ApiResponse;
import com.minmetals.service.FileUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/file-upload")
@RequiredArgsConstructor
@Tag(name = "文件上传", description = "文件上传相关接口")
public class FileUploadController {

    private final FileUploadService fileUploadService;

    /**
     * 上传背景图片
     */
    @PostMapping(value = "/background-image", consumes = "multipart/form-data")
    @Operation(summary = "上传背景图片", description = "上传背景图片并返回访问URL")
    public ApiResponse<String> uploadBackgroundImage(
            @Parameter(description = "图片文件", required = true, schema = @Schema(type = "string", format = "binary"))
            @RequestPart("file") MultipartFile file) {

        log.info("上传背景图片请求，文件名: {}", file.getOriginalFilename());

        try {
            if (file.isEmpty()) {
                return ApiResponse.error("请选择要上传的文件");
            }

            String imageUrl = fileUploadService.uploadBackgroundImage(file);
            return ApiResponse.success("上传成功", imageUrl);

        } catch (Exception e) {
            log.error("上传背景图片失败，文件名: {}", file.getOriginalFilename(), e);
            return ApiResponse.error("上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传头像图片
     */
    @PostMapping(value = "/avatar-image", consumes = "multipart/form-data")
    @Operation(summary = "上传头像图片", description = "上传头像图片并返回访问URL")
    public ApiResponse<String> uploadAvatarImage(
            @Parameter(description = "图片文件", required = true, schema = @Schema(type = "string", format = "binary"))
            @RequestPart("file") MultipartFile file) {

        log.info("上传头像图片请求，文件名: {}", file.getOriginalFilename());

        try {
            if (file.isEmpty()) {
                return ApiResponse.error("请选择要上传的文件");
            }

            String imageUrl = fileUploadService.uploadAvatarImage(file);
            return ApiResponse.success("上传成功", imageUrl);

        } catch (Exception e) {
            log.error("上传头像图片失败，文件名: {}", file.getOriginalFilename(), e);
            return ApiResponse.error("上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/file")
    @Operation(summary = "删除文件", description = "根据文件URL删除文件")
    public ApiResponse<String> deleteFile(
            @Parameter(description = "文件URL", required = true)
            @RequestParam("fileUrl") String fileUrl) {

        log.info("删除文件请求，URL: {}", fileUrl);

        try {
            boolean deleted = fileUploadService.deleteFile(fileUrl);
            if (deleted) {
                return ApiResponse.success("删除成功", "文件已删除");
            } else {
                return ApiResponse.error("删除失败，文件不存在或无法删除");
            }

        } catch (Exception e) {
            log.error("删除文件失败，URL: {}", fileUrl, e);
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 测试文件上传
     */
    @PostMapping(value = "/test", consumes = "multipart/form-data")
    @Operation(summary = "测试文件上传", description = "简单的文件上传测试接口")
    public ApiResponse<String> testFileUpload(
            @Parameter(description = "测试文件", required = true, schema = @Schema(type = "string", format = "binary"))
            @RequestPart("file") MultipartFile file) {

        log.info("测试文件上传，文件名: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());

        try {
            if (file.isEmpty()) {
                return ApiResponse.error("请选择要上传的文件");
            }

            return ApiResponse.success("文件上传测试成功",
                String.format("文件名: %s, 大小: %d bytes, 类型: %s",
                    file.getOriginalFilename(), file.getSize(), file.getContentType()));

        } catch (Exception e) {
            log.error("测试文件上传失败", e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }
}
