/* pages/login/login.wxss */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #dcdcdd 0%, #f5f5f5 100%);
  display: flex;
  flex-direction: column;
  padding: 40rpx;
}

.header {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0 40rpx 0;
  margin-bottom: -350rpx;
}

.logo-section {
  text-align: center;
}

.logo {
  width: 350rpx;
  height: 200rpx;
}

.company-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #221815;
  margin-bottom: 12rpx;
  letter-spacing: 4rpx;
}

.company-name-en {
  display: block;
  font-size: 24rpx;
  color: #727171;
  letter-spacing: 1rpx;
}

.form-container {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-card {
  width: 100%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.form-title {
  text-align: center;
  margin-bottom: 60rpx;
}

.title-text {
  display: block;
  font-size: 42rpx;
  font-weight: bold;
  color: #221815;
  margin-bottom: 12rpx;
}

.subtitle-text {
  display: block;
  font-size: 28rpx;
  color: #727171;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #221815;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 32rpx;
  background-color: #ffffff;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  border-color: #c70019;
  outline: none;
}

.form-input::placeholder {
  color: #999999;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #c70019 0%, #a50015 100%);
  border: none;
  border-radius: 12rpx;
  font-size: 36rpx;
  color: #ffffff;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(199, 0, 25, 0.3);
  transition: all 0.3s ease;
}

.login-btn:not([disabled]):active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(199, 0, 25, 0.4);
}

.login-btn[disabled] {
  background: #cccccc;
  box-shadow: none;
  color: #999999;
}

.login-btn.loading {
  background: #999999;
}

.tips {
  margin-top: 40rpx;
  text-align: center;
}

.tips-text {
  font-size: 24rpx;
  color: #727171;
  line-height: 1.5;
}

.footer {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding: 40rpx 0;
}

.footer-text {
  font-size: 24rpx;
  color: #727171;
}

/* 响应式设计已通过rpx单位实现，无需媒体查询 */
