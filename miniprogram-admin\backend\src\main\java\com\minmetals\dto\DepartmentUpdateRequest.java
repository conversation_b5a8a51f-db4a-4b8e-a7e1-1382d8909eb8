package com.minmetals.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 部门更新请求DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class DepartmentUpdateRequest {

    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空")
    private String name;

    /**
     * 部门描述
     */
    private String description;

    /**
     * 部门负责人
     */
    private String manager;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 办公地址
     */
    private String address;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 是否启用
     */
    private Boolean enabled;
}
