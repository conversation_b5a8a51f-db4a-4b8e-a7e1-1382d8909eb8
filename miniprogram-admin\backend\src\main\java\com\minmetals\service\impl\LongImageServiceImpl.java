package com.minmetals.service.impl;

import com.minmetals.dto.LongImageDTO;
import com.minmetals.entity.LongImage;
import com.minmetals.repository.LongImageRepository;
import com.minmetals.service.LongImageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 长图展示服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class LongImageServiceImpl implements LongImageService {

    private final LongImageRepository longImageRepository;

    @Override
    public LongImageDTO getById(Long id) {
        log.debug("获取ID为 {} 的长图详情", id);
        
        LongImage longImage = longImageRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("长图不存在: " + id));
        
        return convertToDTO(longImage);
    }

    @Override
    public List<LongImageDTO> getAllEnabled() {
        log.debug("获取所有启用的长图");
        
        List<LongImage> longImages = longImageRepository.findByEnabledTrueOrderBySortOrderAscCreatedAtDesc();
        return convertToDTOList(longImages);
    }

    @Override
    public List<LongImageDTO> getByCategory(String category) {
        log.debug("根据分类获取长图，分类: {}", category);
        
        if (category == null || category.trim().isEmpty()) {
            return getAllEnabled();
        }
        
        List<LongImage> longImages = longImageRepository.findByCategoryAndEnabledTrueOrderBySortOrderAsc(category.trim());
        return convertToDTOList(longImages);
    }

    @Override
    @Transactional
    public LongImageDTO createLongImage(LongImageDTO dto) {
        log.debug("创建长图，标题: {}", dto.getTitle());

        LongImage longImage = new LongImage();
        BeanUtils.copyProperties(dto, longImage, "id", "createdAt", "updatedAt");

        longImage = longImageRepository.save(longImage);
        log.info("成功创建长图，ID: {}", longImage.getId());

        return convertToDTO(longImage);
    }

    @Override
    @Transactional
    public LongImageDTO updateLongImage(Long id, LongImageDTO dto) {
        log.debug("更新长图，ID: {}", id);

        LongImage longImage = longImageRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("长图不存在: " + id));

        // 更新字段
        if (dto.getCategory() != null) {
            longImage.setCategory(dto.getCategory());
        }
        if (dto.getTitle() != null) {
            longImage.setTitle(dto.getTitle());
        }
        if (dto.getImageUrl() != null) {
            longImage.setImageUrl(dto.getImageUrl());
        }
        if (dto.getDescription() != null) {
            longImage.setDescription(dto.getDescription());
        }
        if (dto.getWidth() != null) {
            longImage.setWidth(dto.getWidth());
        }
        if (dto.getHeight() != null) {
            longImage.setHeight(dto.getHeight());
        }
        if (dto.getFileSize() != null) {
            longImage.setFileSize(dto.getFileSize());
        }
        if (dto.getFormat() != null) {
            longImage.setFormat(dto.getFormat());
        }
        if (dto.getEnabled() != null) {
            longImage.setEnabled(dto.getEnabled());
        }
        if (dto.getSortOrder() != null) {
            longImage.setSortOrder(dto.getSortOrder());
        }
        if (dto.getCreator() != null) {
            longImage.setCreator(dto.getCreator());
        }

        longImage = longImageRepository.save(longImage);
        log.info("成功更新长图，ID: {}", longImage.getId());

        return convertToDTO(longImage);
    }

    @Override
    @Transactional
    public void deleteLongImage(Long id) {
        log.debug("删除长图，ID: {}", id);

        LongImage longImage = longImageRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("长图不存在: " + id));

        longImageRepository.delete(longImage);
        log.info("成功删除长图，ID: {}", id);
    }

    @Override
    @Transactional
    public LongImageDTO toggleStatus(Long id) {
        log.debug("切换长图状态，ID: {}", id);

        LongImage longImage = longImageRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("长图不存在: " + id));

        longImage.setEnabled(!longImage.getEnabled());
        longImage = longImageRepository.save(longImage);

        log.info("成功切换长图状态，ID: {}，新状态: {}", id, longImage.getEnabled());

        return convertToDTO(longImage);
    }

    @Override
    public List<LongImageDTO> searchByTitle(String keyword) {
        log.debug("根据标题搜索长图，关键词: {}", keyword);
        
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllEnabled();
        }

        List<LongImage> longImages = longImageRepository.searchByTitle(keyword.trim());
        return convertToDTOList(longImages);
    }

    @Override
    public List<LongImageDTO> searchByCategory(String keyword) {
        log.debug("根据分类搜索长图，关键词: {}", keyword);
        
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllEnabled();
        }

        List<LongImage> longImages = longImageRepository.searchByCategory(keyword.trim());
        return convertToDTOList(longImages);
    }

    @Override
    public List<LongImageDTO> getByCreator(String creator) {
        log.debug("根据创建者获取长图，创建者: {}", creator);
        
        if (creator == null || creator.trim().isEmpty()) {
            return getAllEnabled();
        }

        List<LongImage> longImages = longImageRepository.findByCreatorAndEnabledTrueOrderBySortOrderAscCreatedAtDesc(creator.trim());
        return convertToDTOList(longImages);
    }

    @Override
    public List<String> getAllCategories() {
        log.debug("获取所有分类");
        
        return longImageRepository.findAllCategories();
    }

    @Override
    public LongImageDTO convertToDTO(LongImage entity) {
        if (entity == null) {
            return null;
        }

        LongImageDTO dto = new LongImageDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public List<LongImageDTO> convertToDTOList(List<LongImage> entities) {
        if (entities == null || entities.isEmpty()) {
            return new ArrayList<>();
        }

        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
}
