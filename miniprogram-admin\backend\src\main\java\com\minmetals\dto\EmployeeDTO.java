package com.minmetals.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 员工信息DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class EmployeeDTO {

    /**
     * 员工ID
     */
    private Long id;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 传真号码
     */
    private String employeeId;

    /**
     * 部门
     */
    private String department;

    /**
     * 职位
     */
    private String position;

    /**
     * 级别
     */
    private String level;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 地址
     */
    private String address;

    /**
     * 手机号码
     */
    private String avatar;

    /**
     * 邮政编码
     */
    private String manager;

    /**
     * 工作地点
     */
    private String workLocation;

    /**
     * 工作描述
     */
    private String description;

    /**
     * 入职时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date hireDate;
}
