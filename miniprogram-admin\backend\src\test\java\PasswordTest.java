import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public class PasswordTest {
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        String rawPassword = "123456";
        String hashedPassword = "$2a$10$N.zmdr9k7uOkXfJlXlXJ.OJ8ZAbDvIQ9d3D5XkTgzUPmy/SIGEway";
        
        System.out.println("Raw password: " + rawPassword);
        System.out.println("Hashed password: " + hashedPassword);
        System.out.println("Password matches: " + encoder.matches(rawPassword, hashedPassword));
        
        // 生成新的哈希
        String newHash = encoder.encode(rawPassword);
        System.out.println("New hash: " + newHash);
        System.out.println("New hash matches: " + encoder.matches(rawPassword, newHash));
    }
}
