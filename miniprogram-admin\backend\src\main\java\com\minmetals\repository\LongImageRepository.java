package com.minmetals.repository;

import com.minmetals.entity.LongImage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 长图展示数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface LongImageRepository extends JpaRepository<LongImage, Long> {

    /**
     * 根据分类查找长图
     */
    List<LongImage> findByCategoryAndEnabledTrueOrderBySortOrderAsc(String category);

    /**
     * 查找所有启用的长图
     */
    List<LongImage> findByEnabledTrueOrderBySortOrderAscCreatedAtDesc();

    /**
     * 根据图片URL查找长图
     */
    Optional<LongImage> findByImageUrl(String imageUrl);

    /**
     * 统计启用的长图数量
     */
    @Query("SELECT COUNT(l) FROM LongImage l WHERE l.enabled = true")
    long countEnabledLongImages();

    /**
     * 根据标题模糊搜索长图
     */
    @Query("SELECT l FROM LongImage l WHERE l.enabled = true AND " +
           "LOWER(l.title) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "ORDER BY l.sortOrder ASC, l.createdAt DESC")
    List<LongImage> searchByTitle(@Param("keyword") String keyword);

    /**
     * 根据分类模糊搜索长图
     */
    @Query("SELECT l FROM LongImage l WHERE l.enabled = true AND " +
           "LOWER(l.category) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "ORDER BY l.sortOrder ASC, l.createdAt DESC")
    List<LongImage> searchByCategory(@Param("keyword") String keyword);

    /**
     * 根据创建者查找长图
     */
    List<LongImage> findByCreatorAndEnabledTrueOrderBySortOrderAscCreatedAtDesc(String creator);

    /**
     * 获取所有分类
     */
    @Query("SELECT DISTINCT l.category FROM LongImage l WHERE l.enabled = true AND l.category IS NOT NULL ORDER BY l.category")
    List<String> findAllCategories();
}
