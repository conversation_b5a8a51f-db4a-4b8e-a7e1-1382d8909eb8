package com.minmetals.service.impl;

import com.minmetals.dto.BackgroundImageCreateRequest;
import com.minmetals.dto.BackgroundImageUpdateRequest;
import com.minmetals.entity.BackgroundImage;
import com.minmetals.repository.BackgroundImageRepository;
import com.minmetals.service.BackgroundImageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 背景图片服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class BackgroundImageServiceImpl implements BackgroundImageService {

    private final BackgroundImageRepository backgroundImageRepository;

    @Override
    public BackgroundImage getCurrentBackgroundImage() {
        log.debug("获取当前背景图片");
        return backgroundImageRepository.findFirstByEnabledTrueOrderBySortOrderAscCreatedAtDesc()
                .orElse(null);
    }

    @Override
    public List<BackgroundImage> getAllEnabledBackgroundImages() {
        log.debug("获取所有启用的背景图片");
        return backgroundImageRepository.findByEnabledTrueOrderBySortOrderAscCreatedAtDesc();
    }

    @Override
    public BackgroundImage getBackgroundImageById(Long id) {
        log.debug("获取ID为 {} 的背景图片", id);
        return backgroundImageRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("背景图片不存在: " + id));
    }

    @Override
    public List<BackgroundImage> getAllBackgroundImages() {
        log.debug("获取所有背景图片");
        return backgroundImageRepository.findAll();
    }

    @Override
    @Transactional
    public BackgroundImage createBackgroundImage(BackgroundImageCreateRequest request) {
        log.debug("创建背景图片: {}", request.getName());

        // 检查名称是否已存在
        if (backgroundImageRepository.existsByName(request.getName())) {
            throw new RuntimeException("背景图片名称已存在: " + request.getName());
        }

        BackgroundImage backgroundImage = new BackgroundImage();
        BeanUtils.copyProperties(request, backgroundImage);

        backgroundImage = backgroundImageRepository.save(backgroundImage);
        log.info("成功创建背景图片: {}", backgroundImage.getName());

        return backgroundImage;
    }

    @Override
    @Transactional
    public BackgroundImage updateBackgroundImage(Long id, BackgroundImageUpdateRequest request) {
        log.debug("更新背景图片: {}", id);

        BackgroundImage backgroundImage = backgroundImageRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("背景图片不存在: " + id));

        // 检查名称是否已存在（排除当前图片）
        if (StringUtils.hasText(request.getName()) &&
            backgroundImageRepository.existsByNameAndIdNot(request.getName(), id)) {
            throw new RuntimeException("背景图片名称已存在: " + request.getName());
        }

        // 更新字段
        if (StringUtils.hasText(request.getName())) {
            backgroundImage.setName(request.getName());
        }
        if (StringUtils.hasText(request.getImageUrl())) {
            backgroundImage.setImageUrl(request.getImageUrl());
        }
        if (StringUtils.hasText(request.getDescription())) {
            backgroundImage.setDescription(request.getDescription());
        }
        if (request.getEnabled() != null) {
            backgroundImage.setEnabled(request.getEnabled());
        }
        if (request.getSortOrder() != null) {
            backgroundImage.setSortOrder(request.getSortOrder());
        }

        backgroundImage = backgroundImageRepository.save(backgroundImage);
        log.info("成功更新背景图片: {}", backgroundImage.getName());

        return backgroundImage;
    }

    @Override
    @Transactional
    public void deleteBackgroundImage(Long id) {
        log.debug("删除背景图片: {}", id);

        BackgroundImage backgroundImage = backgroundImageRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("背景图片不存在: " + id));

        backgroundImageRepository.delete(backgroundImage);
        log.info("成功删除背景图片: {}", backgroundImage.getName());
    }

    @Override
    @Transactional
    public BackgroundImage toggleBackgroundImageStatus(Long id) {
        log.debug("切换背景图片状态: {}", id);

        BackgroundImage backgroundImage = backgroundImageRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("背景图片不存在: " + id));

        backgroundImage.setEnabled(!backgroundImage.getEnabled());
        backgroundImage = backgroundImageRepository.save(backgroundImage);

        log.info("成功切换背景图片 {} 状态为: {}", backgroundImage.getName(), backgroundImage.getEnabled());

        return backgroundImage;
    }

    @Override
    public List<BackgroundImage> searchBackgroundImages(String keyword) {
        log.debug("搜索背景图片，关键词: {}", keyword);
        if (!StringUtils.hasText(keyword)) {
            return getAllBackgroundImages();
        }
        return backgroundImageRepository.searchBackgroundImages(keyword.trim());
    }
}
