package com.minmetals.controller;

import com.minmetals.dto.*;
import com.minmetals.service.EmployeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 员工控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/employees")
@RequiredArgsConstructor
@Tag(name = "员工管理", description = "员工信息管理相关接口")
public class EmployeeController {

    private final EmployeeService employeeService;

    /**
     * 获取所有员工列表
     */
    @GetMapping
    @Operation(summary = "获取员工列表", description = "获取所有员工列表，支持按关键词、部门、职位筛选")
    public ApiResponse<List<EmployeeDTO>> getAllEmployees(
            @Parameter(description = "搜索关键词") @RequestParam(value = "keyword", required = false) String keyword,
            @Parameter(description = "部门名称") @RequestParam(value = "department", required = false) String department,
            @Parameter(description = "职位名称") @RequestParam(value = "position", required = false) String position) {

        log.info("获取员工列表，关键词: {}, 部门: {}, 职位: {}", keyword, department, position);

        try {
            List<EmployeeDTO> employees;

            if (keyword != null && !keyword.trim().isEmpty()) {
                // 关键词搜索
                employees = employeeService.searchEmployees(keyword);
            } else if (department != null && !department.trim().isEmpty()) {
                // 按部门筛选
                employees = employeeService.getEmployeesByDepartment(department);
            } else if (position != null && !position.trim().isEmpty()) {
                // 按职位筛选
                employees = employeeService.getEmployeesByPosition(position);
            } else {
                // 获取所有员工
                employees = employeeService.getAllEmployees();
            }

            return ApiResponse.success("获取员工列表成功", employees);

        } catch (Exception e) {
            log.error("获取员工列表时发生异常", e);
            return ApiResponse.error("获取员工列表失败");
        }
    }

    /**
     * 根据ID获取员工详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取员工详情", description = "根据员工ID获取员工详细信息")
    public ApiResponse<EmployeeDTO> getEmployeeById(@Parameter(description = "员工ID") @PathVariable Long id) {
        log.info("获取员工详情，ID: {}", id);

        try {
            EmployeeDTO employee = employeeService.getEmployeeById(id);
            return ApiResponse.success("获取员工详情成功", employee);
        } catch (RuntimeException e) {
            log.warn("获取员工详情失败: {}", e.getMessage());
            return ApiResponse.notFound(e.getMessage());
        } catch (Exception e) {
            log.error("获取员工详情时发生异常", e);
            return ApiResponse.error("获取员工详情失败");
        }
    }



    /**
     * 搜索员工
     */
    @GetMapping("/search")
    @Operation(summary = "搜索员工", description = "根据关键词搜索员工")
    public ApiResponse<List<EmployeeDTO>> searchEmployees(
            @Parameter(description = "搜索关键词") @RequestParam("keyword") String keyword) {

        log.info("搜索员工，关键词: {}", keyword);

        try {
            List<EmployeeDTO> employees = employeeService.searchEmployees(keyword);
            return ApiResponse.success("搜索员工成功", employees);
        } catch (Exception e) {
            log.error("搜索员工时发生异常", e);
            return ApiResponse.error("搜索员工失败");
        }
    }

    /**
     * 根据部门获取员工列表
     */
    @GetMapping("/department/{department}")
    @Operation(summary = "按部门获取员工", description = "根据部门名称获取员工列表")
    public ApiResponse<List<EmployeeDTO>> getEmployeesByDepartment(
            @Parameter(description = "部门名称") @PathVariable String department) {

        log.info("获取部门员工列表，部门: {}", department);

        try {
            List<EmployeeDTO> employees = employeeService.getEmployeesByDepartment(department);
            return ApiResponse.success("获取部门员工列表成功", employees);
        } catch (Exception e) {
            log.error("获取部门员工列表时发生异常", e);
            return ApiResponse.error("获取部门员工列表失败");
        }
    }

    /**
     * 根据职位获取员工列表
     */
    @GetMapping("/position/{position}")
    public ApiResponse<List<EmployeeDTO>> getEmployeesByPosition(
            @PathVariable String position) {

        log.info("获取职位员工列表，职位: {}", position);

        try {
            List<EmployeeDTO> employees = employeeService.getEmployeesByPosition(position);
            return ApiResponse.success("获取职位员工列表成功", employees);
        } catch (Exception e) {
            log.error("获取职位员工列表时发生异常", e);
            return ApiResponse.error("获取职位员工列表失败");
        }
    }

    /**
     * 创建员工
     */
    @PostMapping
    @Operation(summary = "创建员工", description = "创建新的员工")
    public ApiResponse<EmployeeDTO> createEmployee(@Valid @RequestBody EmployeeCreateRequest request) {
        log.info("创建员工，用户名: {}", request.getUsername());
        try {
            EmployeeDTO employee = employeeService.createEmployee(request);
            return ApiResponse.success("创建成功", employee);
        } catch (Exception e) {
            log.error("创建员工失败，用户名: {}", request.getUsername(), e);
            return ApiResponse.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新员工
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新员工", description = "更新员工信息")
    public ApiResponse<EmployeeDTO> updateEmployee(
            @Parameter(description = "员工ID") @PathVariable Long id,
            @Valid @RequestBody EmployeeUpdateRequest request) {
        log.info("更新员工，ID: {}", id);
        try {
            EmployeeDTO employee = employeeService.updateEmployee(id, request);
            return ApiResponse.success("更新成功", employee);
        } catch (Exception e) {
            log.error("更新员工失败，ID: {}", id, e);
            return ApiResponse.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除员工
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除员工", description = "删除员工")
    public ApiResponse<String> deleteEmployee(
            @Parameter(description = "员工ID") @PathVariable Long id) {
        log.info("删除员工，ID: {}", id);
        try {
            employeeService.deleteEmployee(id);
            return ApiResponse.success("删除成功", "员工已删除");
        } catch (Exception e) {
            log.error("删除员工失败，ID: {}", id, e);
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用员工
     */
    @PatchMapping("/{id}/toggle-status")
    @Operation(summary = "启用/禁用员工", description = "切换员工的启用状态")
    public ApiResponse<EmployeeDTO> toggleEmployeeStatus(
            @Parameter(description = "员工ID") @PathVariable Long id) {
        log.info("切换员工状态，ID: {}", id);
        try {
            EmployeeDTO employee = employeeService.toggleEmployeeStatus(id);
            return ApiResponse.success("状态切换成功", employee);
        } catch (Exception e) {
            log.error("切换员工状态失败，ID: {}", id, e);
            return ApiResponse.error("状态切换失败: " + e.getMessage());
        }
    }

    /**
     * 根据部门ID获取员工列表
     */
    @GetMapping("/department-id/{departmentId}")
    @Operation(summary = "按部门ID获取员工", description = "根据部门ID获取员工列表")
    public ApiResponse<List<EmployeeDTO>> getEmployeesByDepartmentId(
            @Parameter(description = "部门ID") @PathVariable Long departmentId) {

        log.info("获取部门员工列表，部门ID: {}", departmentId);

        try {
            List<EmployeeDTO> employees = employeeService.getEmployeesByDepartmentId(departmentId);
            return ApiResponse.success("获取部门员工列表成功", employees);
        } catch (Exception e) {
            log.error("获取部门员工列表时发生异常", e);
            return ApiResponse.error("获取部门员工列表失败");
        }
    }

    /**
     * 根据部门ID获取启用状态的员工列表
     */
    @GetMapping("/department-id/{departmentId}/enabled")
    @Operation(summary = "按部门ID获取启用员工", description = "根据部门ID获取启用状态的员工列表")
    public ApiResponse<List<EmployeeDTO>> getEnabledEmployeesByDepartmentId(
            @Parameter(description = "部门ID") @PathVariable Long departmentId) {

        log.info("获取部门启用员工列表，部门ID: {}", departmentId);

        try {
            List<EmployeeDTO> employees = employeeService.getEnabledEmployeesByDepartmentId(departmentId);
            return ApiResponse.success("获取部门启用员工列表成功", employees);
        } catch (Exception e) {
            log.error("获取部门启用员工列表时发生异常", e);
            return ApiResponse.error("获取部门启用员工列表失败");
        }
    }
}
