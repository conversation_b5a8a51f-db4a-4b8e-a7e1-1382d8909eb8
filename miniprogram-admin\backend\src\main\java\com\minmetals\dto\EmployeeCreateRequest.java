package com.minmetals.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 员工创建请求DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Schema(description = "员工创建请求")
public class EmployeeCreateRequest {

    @Schema(description = "员工姓名", required = true)
    @NotBlank(message = "员工姓名不能为空")
    @Size(max = 50, message = "员工姓名长度不能超过50个字符")
    private String name;

    @Schema(description = "传真号码", required = true)
    @NotBlank(message = "传真号码不能为空")
    @Size(max = 20, message = "传真号码长度不能超过20个字符")
    private String employeeId;

    @Schema(description = "登录用户名", required = true)
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;

    @Schema(description = "登录密码", required = true)
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 255, message = "密码长度必须在6-255个字符之间")
    private String password;

    @Schema(description = "部门")
    @Size(max = 100, message = "部门长度不能超过100个字符")
    private String department;

    @Schema(description = "职位")
    @Size(max = 100, message = "职位长度不能超过100个字符")
    private String position;

    @Schema(description = "级别")
    @Size(max = 50, message = "级别长度不能超过50个字符")
    private String level;

    @Schema(description = "电话号码")
    @Size(max = 20, message = "电话号码长度不能超过20个字符")
    private String phone;

    @Schema(description = "邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    @Schema(description = "地址")
    @Size(max = 200, message = "地址长度不能超过200个字符")
    private String address;

    @Schema(description = "手机号码")
    @Size(max = 20, message = "手机号码长度不能超过20个字符")
    private String avatar;

    @Schema(description = "邮政编码")
    @Size(max = 10, message = "邮政编码长度不能超过10个字符")
    private String manager;

    @Schema(description = "工作地点")
    @Size(max = 100, message = "工作地点长度不能超过100个字符")
    private String workLocation;

    @Schema(description = "描述")
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;

    @Schema(description = "入职日期")
    private LocalDate hireDate;

    @Schema(description = "是否启用")
    private Boolean enabled = true;
}
