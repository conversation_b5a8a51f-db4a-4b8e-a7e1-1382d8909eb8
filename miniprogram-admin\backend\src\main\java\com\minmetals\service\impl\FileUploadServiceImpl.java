package com.minmetals.service.impl;

import com.minmetals.service.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件上传服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class FileUploadServiceImpl implements FileUploadService {

    @Value("${file.upload.path:uploads}")
    private String uploadPath;

    @Value("${file.upload.max-size:10485760}") // 10MB
    private long maxFileSize;

    @Value("${server.servlet.context-path:/api}")
    private String contextPath;

    @Value("${server.port:8080}")
    private String serverPort;

    // 支持的图片格式
    private static final List<String> ALLOWED_IMAGE_EXTENSIONS = Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "webp"
    );

    // 支持的图片MIME类型
    private static final List<String> ALLOWED_IMAGE_MIME_TYPES = Arrays.asList(
            "image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp"
    );

    @Override
    public String uploadBackgroundImage(MultipartFile file) {
        log.info("开始上传背景图片，文件名: {}", file.getOriginalFilename());

        // 验证文件
        if (!isValidImageFile(file)) {
            throw new RuntimeException("不支持的图片格式");
        }

        // 创建背景图片目录
        String subDir = "background";
        return uploadFile(file, subDir);
    }

    @Override
    public String uploadAvatarImage(MultipartFile file) {
        log.info("开始上传头像图片，文件名: {}", file.getOriginalFilename());

        // 验证文件
        if (!isValidImageFile(file)) {
            throw new RuntimeException("不支持的图片格式");
        }

        // 创建头像目录
        String subDir = "avatar";
        return uploadFile(file, subDir);
    }

    @Override
    public String uploadLongImage(MultipartFile file) {
        log.info("开始上传长图，文件名: {}", file.getOriginalFilename());

        // 验证文件
        if (!isValidImageFile(file)) {
            throw new RuntimeException("不支持的图片格式");
        }

        // 创建长图目录
        String subDir = "long-images";
        return uploadFile(file, subDir);
    }

    private String uploadFile(MultipartFile file, String subDir) {
        try {
            // 检查文件大小
            if (file.getSize() > maxFileSize) {
                throw new RuntimeException("文件大小超过限制: " + (maxFileSize / 1024 / 1024) + "MB");
            }

            // 创建上传目录
            String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            Path uploadDir = Paths.get(uploadPath, subDir, dateDir);
            Files.createDirectories(uploadDir);

            // 生成唯一文件名
            String uniqueFilename = generateUniqueFilename(file.getOriginalFilename());
            Path filePath = uploadDir.resolve(uniqueFilename);

            // 保存文件
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

            // 生成访问URL
            String fileUrl = String.format("http://localhost:%s%s/files/%s/%s/%s",
                    serverPort, contextPath, subDir, dateDir, uniqueFilename);

            log.info("文件上传成功，URL: {}", fileUrl);
            return fileUrl;

        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteFile(String fileUrl) {
        try {
            if (!StringUtils.hasText(fileUrl)) {
                return false;
            }

            // 从URL中提取文件路径
            String filePath = extractFilePathFromUrl(fileUrl);
            if (filePath == null) {
                return false;
            }

            Path path = Paths.get(uploadPath, filePath);
            boolean deleted = Files.deleteIfExists(path);

            if (deleted) {
                log.info("文件删除成功: {}", fileUrl);
            } else {
                log.warn("文件不存在或删除失败: {}", fileUrl);
            }

            return deleted;

        } catch (IOException e) {
            log.error("删除文件失败: {}", fileUrl, e);
            return false;
        }
    }

    @Override
    public boolean isValidImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        // 检查文件扩展名
        String extension = getFileExtension(file.getOriginalFilename());
        if (!ALLOWED_IMAGE_EXTENSIONS.contains(extension.toLowerCase())) {
            return false;
        }

        // 检查MIME类型
        String contentType = file.getContentType();
        if (!ALLOWED_IMAGE_MIME_TYPES.contains(contentType)) {
            return false;
        }

        return true;
    }

    @Override
    public String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }

        return filename.substring(lastDotIndex + 1);
    }

    @Override
    public String generateUniqueFilename(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis());

        if (StringUtils.hasText(extension)) {
            return String.format("%s_%s.%s", uuid, timestamp, extension);
        } else {
            return String.format("%s_%s", uuid, timestamp);
        }
    }

    private String extractFilePathFromUrl(String fileUrl) {
        try {
            // 从URL中提取文件路径
            // 例如: http://localhost:8080/api/files/background/2023/12/01/filename.jpg
            // 提取: background/2023/12/01/filename.jpg

            String filesPrefix = "/files/";
            int filesIndex = fileUrl.indexOf(filesPrefix);
            if (filesIndex == -1) {
                return null;
            }

            return fileUrl.substring(filesIndex + filesPrefix.length());

        } catch (Exception e) {
            log.error("提取文件路径失败: {}", fileUrl, e);
            return null;
        }
    }
}
