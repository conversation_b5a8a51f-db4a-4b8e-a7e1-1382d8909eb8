<template>
  <el-cascader
    v-model="selectedValue"
    :options="departmentOptions"
    :props="cascaderProps"
    placeholder="请选择部门"
    style="width: 100%"
    clearable
    filterable
    @change="handleChange"
  />
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import request from '@/utils/request'

const props = defineProps({
  modelValue: [Number, String],
  placeholder: {
    type: String,
    default: '请选择部门'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const departmentTree = ref([])
const selectedValue = ref([])

const cascaderProps = {
  value: 'id',
  label: 'name',
  children: 'children',
  emitPath: false, // 只返回最后一级的值
  checkStrictly: false // 只能选择叶子节点
}

// 转换部门树为级联选择器格式
const departmentOptions = computed(() => {
  return convertTreeToCascaderOptions(departmentTree.value)
})

// 转换树形数据为级联选择器选项
const convertTreeToCascaderOptions = (tree) => {
  return tree.map(node => ({
    id: node.id,
    name: node.name,
    level: node.level,
    children: node.children && node.children.length > 0 
      ? convertTreeToCascaderOptions(node.children)
      : undefined
  }))
}

// 根据部门ID查找完整路径
const findDepartmentPath = (departmentId, tree = departmentTree.value, path = []) => {
  for (const node of tree) {
    const currentPath = [...path, node.id]
    
    if (node.id === departmentId) {
      return currentPath
    }
    
    if (node.children && node.children.length > 0) {
      const result = findDepartmentPath(departmentId, node.children, currentPath)
      if (result) {
        return result
      }
    }
  }
  return null
}

// 加载部门树
const loadDepartmentTree = async () => {
  try {
    const response = await request.get('/departments/enabled/tree')
    departmentTree.value = response.data || []
    
    // 如果有初始值，设置选中路径
    if (props.modelValue) {
      const path = findDepartmentPath(props.modelValue)
      if (path) {
        selectedValue.value = path
      }
    }
  } catch (error) {
    console.error('加载部门树失败:', error)
  }
}

// 处理选择变化
const handleChange = (value) => {
  const departmentId = Array.isArray(value) ? value[value.length - 1] : value
  emit('update:modelValue', departmentId)
  emit('change', departmentId, value)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && departmentTree.value.length > 0) {
    const path = findDepartmentPath(newValue)
    if (path) {
      selectedValue.value = path
    }
  } else {
    selectedValue.value = []
  }
})

onMounted(() => {
  loadDepartmentTree()
})
</script>
