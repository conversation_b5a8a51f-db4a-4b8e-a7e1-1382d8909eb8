package com.minmetals.service;

import com.minmetals.dto.DepartmentCreateRequest;
import com.minmetals.dto.DepartmentDTO;
import com.minmetals.dto.DepartmentUpdateRequest;
import com.minmetals.entity.Department;

import java.util.List;

/**
 * 部门服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface DepartmentService {

    /**
     * 创建部门
     */
    DepartmentDTO createDepartment(DepartmentCreateRequest request);

    /**
     * 更新部门
     */
    DepartmentDTO updateDepartment(Long id, DepartmentUpdateRequest request);

    /**
     * 删除部门
     */
    void deleteDepartment(Long id);

    /**
     * 根据ID获取部门
     */
    DepartmentDTO getDepartmentById(Long id);

    /**
     * 获取所有部门列表
     */
    List<DepartmentDTO> getAllDepartments();

    /**
     * 获取部门树形结构
     */
    List<DepartmentDTO> getDepartmentTree();

    /**
     * 根据级别获取部门列表
     */
    List<DepartmentDTO> getDepartmentsByLevel(Integer level);

    /**
     * 根据父部门ID获取子部门列表
     */
    List<DepartmentDTO> getChildDepartments(Long parentId);

    /**
     * 获取启用状态的部门列表
     */
    List<DepartmentDTO> getEnabledDepartments();

    /**
     * 获取启用状态的部门树形结构
     */
    List<DepartmentDTO> getEnabledDepartmentTree();

    /**
     * 根据名称搜索部门
     */
    List<DepartmentDTO> searchDepartmentsByName(String name);

    /**
     * 切换部门启用状态
     */
    void toggleDepartmentStatus(Long id);

    /**
     * 获取一级部门列表
     */
    List<DepartmentDTO> getRootDepartments();

    /**
     * 获取启用状态的一级部门列表
     */
    List<DepartmentDTO> getEnabledRootDepartments();

    /**
     * 验证部门编码是否唯一
     */
    boolean isCodeUnique(String code, Long excludeId);

    /**
     * 检查部门是否可以删除（没有子部门和员工）
     */
    boolean canDeleteDepartment(Long id);

    /**
     * 将实体转换为DTO
     */
    DepartmentDTO convertToDTO(Department department);

    /**
     * 构建部门路径
     */
    String buildDepartmentPath(Long parentId);
}
