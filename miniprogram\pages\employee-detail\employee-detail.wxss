/* pages/employee-detail/employee-detail.wxss */
.employee-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #727171;
}

.detail-content {
  padding-bottom: 40rpx;
}

/* 长图展示样式 */
.long-image-section {
  background-color: #ffffff;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-top: -20rpx;
  margin-left: -20rpx;
  margin-right: -20rpx;
}

.long-image-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.long-image-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #221815;
  display: block;
  margin-bottom: 8rpx;
}

.long-image-category {
  font-size: 24rpx;
  color: #c70019;
  background-color: rgba(199, 0, 25, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.long-image-container {
  width: 100%;
  position: relative;
}

.long-image {
  width: 100%;
  display: block;
}

.long-image-description {
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.description-text {
  font-size: 28rpx;
  color: #727171;
  line-height: 1.6;
}

.long-image-loading {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 响应式设计已通过rpx单位实现，无需媒体查询 */
.section_1 {
  width: 750rpx;
  height: 458rpx;
  background: url(https://lanhu-oss-2537-2.lanhuapp.com/psks5hc79126grofmp7rgqchclo16tzqyec4f34dd7-74c6-4b38-a2bb-5812a0758c67) 0rpx 0rpx no-repeat;
  background-size: 1532rpx 461rpx;
  flex-direction: row;
  display: flex;
}
.image_1 {
  width: 188rpx;
  height: 149rpx;
  margin: 140rpx 0 0 35rpx;
}
.text_1 {
  width: 310rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgba(35,24,21,1);
  font-size: 36rpx;
  font-family: SourceHanSansCN-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 39rpx;
  margin: 180rpx 139rpx 0 55rpx;
}
.section_2 {
  position: absolute;
  left: 0rpx;
  top: 0rpx;
  width: 750rpx;
  height: 458rpx;
  background: url(https://lanhu-oss-2537-2.lanhuapp.com/psj3n4vb18poey6f3h63lpkr1ty1hmpfeltec6b3480-e0ab-4a8b-8d8a-37d2007ed9fd) 0rpx 0rpx no-repeat;
  background-size: 761rpx 461rpx;
  flex-direction: row;
  display: flex;
}
.box_1 {
  width: 750rpx;
  height: 269rpx;
  margin-top: 176rpx;
  display: flex;
  flex-direction: column;
}
.text_2 {
  width: 98rpx;
  height: 39rpx;
  overflow-wrap: break-word;
  color: rgba(35,24,21,1);
  font-size: 41rpx;
  font-family: SourceHanSansCN-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 41rpx;
  margin: -93rpx 0 0 298rpx;
}
.text_3 {
  width: 220rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgba(35,24,21,1);
  font-size: 26rpx;
  font-family: SourceHanSansCN-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin: 21rpx 0 0 297rpx;
}
.image_2 {
  width: 462rpx;
  height: 11rpx;
  margin: 84rpx 0 0 288rpx;
}
.text_4 {
  width: 273rpx;
  height: 19rpx;
  overflow-wrap: break-word;
  color: rgba(35,24,21,1);
  font-size: 20rpx;
  font-family: SourceHanSansCN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
  margin: 7rpx 0 0 297rpx;
}
.text_5 {
  width: 174rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgba(35,24,21,1);
  font-size: 20rpx;
  font-family: SourceHanSansCN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
  margin: 8rpx 0 0 297rpx;
}
.text_6 {
  width: 176rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgba(35,24,21,1);
  font-size: 20rpx;
  font-family: SourceHanSansCN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
  margin: 8rpx 0 0 298rpx;
}
.group_1 {
  width: 357rpx;
  height: 18rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 8rpx 0 0 296rpx;
}
.text_7 {
  width: 78rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgba(35,24,21,1);
  font-size: 20rpx;
  font-family: SourceHanSansCN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
}
.text-wrapper_1 {
  width: 111rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: SimSun;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
}
.text_8 {
  width: 111rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgba(35, 24, 21, 1);
  font-size: 20rpx;
  font-family: SimSun;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
}
.text_9 {
  width: 111rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgba(35, 24, 21, 1);
  font-size: 20rpx;
  font-family: SourceHanSansCN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
}
.text_10 {
  width: 269rpx;
  height: 21rpx;
  overflow-wrap: break-word;
  color: rgba(35,24,21,1);
  font-size: 20rpx;
  font-family: SourceHanSansCN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
  margin: 8rpx 0 0 298rpx;
}
.image_3 {
  width: 750rpx;
  height: 4rpx;
  margin-top: 10rpx;
}
.image_4 {
  width: 750rpx;
  height: 5rpx;
  margin-top: 1rpx;
}
.image_5 {
  width: 750rpx;
  height: 4rpx;
  margin-top: 1rpx;
}
.image_6 {
  width: 750rpx;
  height: 4rpx;
  margin-top: 2rpx;
}
.image_7 {
  width: 750rpx;
  height: 4rpx;
  margin-top: 2rpx;
}
.image_8 {
  width: 750rpx;
  height: 5rpx;
  margin-top: 1rpx;
}
.image_9 {
  width: 750rpx;
  height: 4rpx;
  margin-top: 1rpx;
}
.image_10 {
  width: 750rpx;
  height: 4rpx;
  margin: 1rpx 0 1rpx 0;
}
.image_11 {
  width: 750rpx;
  height: 5rpx;
  margin: 439rpx 0 0 -750rpx;
}
.image_12 {
  position: absolute;
  left: 0rpx;
  top: 0rpx;
  width: 750rpx;
  height: 176rpx;
}

/* 功能按钮组样式 */
.action-buttons {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 20rpx 0rpx;
  margin: 20rpx;
  margin-left: -3rpx;
  border-radius: 12rpx;
  gap: 30rpx;
}

.action-btn {
  height: 80rpx;
  width: 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #c70019;
  border: none;
  padding: 0 10rpx;
  margin-top: -50rpx;
  line-height: 1.2;
}

.action-btn::after {
  border: none;
}

.action-btn text {
  font-size: 28rpx;
  color: #ffffff;
}