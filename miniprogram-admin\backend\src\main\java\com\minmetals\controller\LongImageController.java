package com.minmetals.controller;

import com.minmetals.dto.ApiResponse;
import com.minmetals.dto.LongImageDTO;
import com.minmetals.service.LongImageService;
import com.minmetals.service.FileUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 长图展示控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/long-images")
@RequiredArgsConstructor
@Tag(name = "长图展示管理", description = "长图展示信息管理相关接口")
public class LongImageController {

    private final LongImageService longImageService;
    private final FileUploadService fileUploadService;

    /**
     * 获取所有启用的长图
     */
    @GetMapping
    @Operation(summary = "获取所有启用的长图", description = "获取所有启用的长图列表")
    public ApiResponse<List<LongImageDTO>> getAllEnabledLongImages() {
        log.info("获取所有启用的长图");

        try {
            List<LongImageDTO> longImages = longImageService.getAllEnabled();
            return ApiResponse.success("获取长图列表成功", longImages);
        } catch (Exception e) {
            log.error("获取长图列表时发生异常", e);
            return ApiResponse.error("获取长图列表失败");
        }
    }

    /**
     * 根据ID获取长图详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取长图详情", description = "根据长图ID获取详细信息")
    public ApiResponse<LongImageDTO> getLongImageById(@Parameter(description = "长图ID") @PathVariable Long id) {
        log.info("获取长图详情，ID: {}", id);

        try {
            LongImageDTO longImage = longImageService.getById(id);
            return ApiResponse.success("获取长图详情成功", longImage);
        } catch (RuntimeException e) {
            log.warn("获取长图详情失败: {}", e.getMessage());
            return ApiResponse.notFound(e.getMessage());
        } catch (Exception e) {
            log.error("获取长图详情时发生异常", e);
            return ApiResponse.error("获取长图详情失败");
        }
    }

    /**
     * 根据分类获取长图
     */
    @GetMapping("/category/{category}")
    @Operation(summary = "根据分类获取长图", description = "根据分类获取该分类的长图")
    public ApiResponse<List<LongImageDTO>> getLongImagesByCategory(
            @Parameter(description = "分类名称") @PathVariable String category) {
        log.info("根据分类获取长图，分类: {}", category);

        try {
            List<LongImageDTO> longImages = longImageService.getByCategory(category);
            return ApiResponse.success("获取分类长图成功", longImages);
        } catch (Exception e) {
            log.error("获取分类长图时发生异常", e);
            return ApiResponse.error("获取分类长图失败");
        }
    }

    /**
     * 获取所有分类
     */
    @GetMapping("/categories")
    @Operation(summary = "获取所有分类", description = "获取所有长图分类列表")
    public ApiResponse<List<String>> getAllCategories() {
        log.info("获取所有分类");

        try {
            List<String> categories = longImageService.getAllCategories();
            return ApiResponse.success("获取分类列表成功", categories);
        } catch (Exception e) {
            log.error("获取分类列表时发生异常", e);
            return ApiResponse.error("获取分类列表失败");
        }
    }

    /**
     * 创建长图
     */
    @PostMapping
    @Operation(summary = "创建长图", description = "创建新的长图")
    public ApiResponse<LongImageDTO> createLongImage(@Valid @RequestBody LongImageDTO dto) {
        log.info("创建长图，标题: {}", dto.getTitle());

        try {
            LongImageDTO longImage = longImageService.createLongImage(dto);
            return ApiResponse.success("创建长图成功", longImage);
        } catch (Exception e) {
            log.error("创建长图失败", e);
            return ApiResponse.error("创建长图失败: " + e.getMessage());
        }
    }

    /**
     * 上传并创建长图
     */
    @PostMapping(value = "/upload", consumes = "multipart/form-data")
    @Operation(summary = "上传并创建长图", description = "上传图片文件并创建长图记录")
    public ApiResponse<LongImageDTO> uploadAndCreateLongImage(
            @Parameter(description = "图片文件", required = true, schema = @Schema(type = "string", format = "binary"))
            @RequestPart("file") MultipartFile file,
            @Parameter(description = "长图标题", required = true)
            @RequestPart("title") String title,
            @Parameter(description = "长图分类")
            @RequestPart(value = "category", required = false) String category,
            @Parameter(description = "长图描述")
            @RequestPart(value = "description", required = false) String description,
            @Parameter(description = "是否启用")
            @RequestPart(value = "enabled", required = false) String enabled,
            @Parameter(description = "排序顺序")
            @RequestPart(value = "sortOrder", required = false) String sortOrder,
            @Parameter(description = "创建者")
            @RequestPart(value = "creator", required = false) String creator) {

        log.info("上传并创建长图，文件名: {}, 标题: {}", file.getOriginalFilename(), title);

        try {
            if (file.isEmpty()) {
                return ApiResponse.error("请选择要上传的文件");
            }

            if (title == null || title.trim().isEmpty()) {
                return ApiResponse.error("长图标题不能为空");
            }

            // 上传图片文件
            String imageUrl = fileUploadService.uploadLongImage(file);

            // 创建长图记录
            LongImageDTO createDto = new LongImageDTO();
            createDto.setTitle(title.trim());
            createDto.setImageUrl(imageUrl);
            createDto.setCategory(category);
            createDto.setDescription(description);
            createDto.setEnabled(enabled != null ? Boolean.parseBoolean(enabled) : true);
            createDto.setSortOrder(sortOrder != null ? Integer.parseInt(sortOrder) : 0);
            createDto.setCreator(creator);

            // 设置图片元数据
            createDto.setFormat(fileUploadService.getFileExtension(file.getOriginalFilename()));
            createDto.setFileSize(file.getSize());

            LongImageDTO longImage = longImageService.createLongImage(createDto);

            return ApiResponse.success("上传并创建成功", longImage);

        } catch (Exception e) {
            log.error("上传并创建长图失败，文件名: {}, 标题: {}", file.getOriginalFilename(), title, e);
            return ApiResponse.error("上传并创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新长图
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新长图", description = "更新长图信息")
    public ApiResponse<LongImageDTO> updateLongImage(
            @Parameter(description = "长图ID") @PathVariable Long id,
            @Valid @RequestBody LongImageDTO dto) {
        log.info("更新长图，ID: {}", id);

        try {
            LongImageDTO longImage = longImageService.updateLongImage(id, dto);
            return ApiResponse.success("更新长图成功", longImage);
        } catch (Exception e) {
            log.error("更新长图失败，ID: {}", id, e);
            return ApiResponse.error("更新长图失败: " + e.getMessage());
        }
    }

    /**
     * 删除长图
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除长图", description = "删除长图")
    public ApiResponse<String> deleteLongImage(@Parameter(description = "长图ID") @PathVariable Long id) {
        log.info("删除长图，ID: {}", id);

        try {
            longImageService.deleteLongImage(id);
            return ApiResponse.success("删除长图成功", "长图已删除");
        } catch (Exception e) {
            log.error("删除长图失败，ID: {}", id, e);
            return ApiResponse.error("删除长图失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用长图
     */
    @PatchMapping("/{id}/toggle-status")
    @Operation(summary = "启用/禁用长图", description = "切换长图的启用状态")
    public ApiResponse<LongImageDTO> toggleLongImageStatus(@Parameter(description = "长图ID") @PathVariable Long id) {
        log.info("切换长图状态，ID: {}", id);

        try {
            LongImageDTO longImage = longImageService.toggleStatus(id);
            return ApiResponse.success("状态切换成功", longImage);
        } catch (Exception e) {
            log.error("切换长图状态失败，ID: {}", id, e);
            return ApiResponse.error("状态切换失败: " + e.getMessage());
        }
    }

    /**
     * 根据标题搜索长图
     */
    @GetMapping("/search")
    @Operation(summary = "搜索长图", description = "根据标题搜索长图")
    public ApiResponse<List<LongImageDTO>> searchLongImages(
            @Parameter(description = "搜索关键词") @RequestParam("keyword") String keyword) {
        log.info("搜索长图，关键词: {}", keyword);

        try {
            List<LongImageDTO> longImages = longImageService.searchByTitle(keyword);
            return ApiResponse.success("搜索长图成功", longImages);
        } catch (Exception e) {
            log.error("搜索长图时发生异常", e);
            return ApiResponse.error("搜索长图失败");
        }
    }
}
