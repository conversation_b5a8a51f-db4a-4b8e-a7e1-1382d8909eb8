package com.minmetals.controller;

import com.minmetals.dto.ApiResponse;
import com.minmetals.entity.BackgroundImage;
import com.minmetals.service.BackgroundImageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 背景图片控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/background")
@RequiredArgsConstructor
@Tag(name = "背景图片管理", description = "背景图片相关接口")
public class BackgroundController {

    private final BackgroundImageService backgroundImageService;

    /**
     * 获取当前背景图片
     */
    @GetMapping("/image")
    @Operation(summary = "获取当前背景图片", description = "获取当前启用的背景图片信息")
    public ApiResponse<Map<String, String>> getCurrentBackgroundImage() {
        log.info("获取当前背景图片");

        try {
            BackgroundImage backgroundImage = backgroundImageService.getCurrentBackgroundImage();

            Map<String, String> result = new HashMap<>();
            if (backgroundImage != null) {
                result.put("imageUrl", backgroundImage.getImageUrl());
                result.put("name", backgroundImage.getName());
                result.put("description", backgroundImage.getDescription());
            } else {
                // 返回默认背景图片信息
                result.put("imageUrl", "/images/default-bg.jpg");
                result.put("name", "默认背景");
                result.put("description", "默认背景图片");
            }

            return ApiResponse.success("获取背景图片成功", result);

        } catch (Exception e) {
            log.error("获取背景图片时发生异常", e);

            // 发生异常时返回默认背景图片
            Map<String, String> result = new HashMap<>();
            result.put("imageUrl", "/images/default-bg.jpg");
            result.put("name", "默认背景");
            result.put("description", "默认背景图片");

            return ApiResponse.success("获取背景图片成功", result);
        }
    }

    /**
     * 获取所有背景图片
     */
    @GetMapping("/images")
    @Operation(summary = "获取所有背景图片", description = "获取所有启用的背景图片列表")
    public ApiResponse<List<BackgroundImage>> getAllBackgroundImages() {
        log.info("获取所有背景图片");

        try {
            List<BackgroundImage> images = backgroundImageService.getAllEnabledBackgroundImages();
            return ApiResponse.success("获取背景图片列表成功", images);
        } catch (Exception e) {
            log.error("获取背景图片列表时发生异常", e);
            return ApiResponse.error("获取背景图片列表失败");
        }
    }

    /**
     * 根据ID获取背景图片
     */
    @GetMapping("/images/{id}")
    @Operation(summary = "获取背景图片详情", description = "根据ID获取背景图片详细信息")
    public ApiResponse<BackgroundImage> getBackgroundImageById(@Parameter(description = "背景图片ID") @PathVariable Long id) {
        log.info("获取背景图片详情，ID: {}", id);

        try {
            BackgroundImage image = backgroundImageService.getBackgroundImageById(id);
            return ApiResponse.success("获取背景图片详情成功", image);
        } catch (RuntimeException e) {
            log.warn("获取背景图片详情失败: {}", e.getMessage());
            return ApiResponse.notFound(e.getMessage());
        } catch (Exception e) {
            log.error("获取背景图片详情时发生异常", e);
            return ApiResponse.error("获取背景图片详情失败");
        }
    }
}
