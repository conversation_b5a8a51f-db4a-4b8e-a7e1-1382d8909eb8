package com.minmetals.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface FileUploadService {

    /**
     * 上传背景图片
     *
     * @param file 图片文件
     * @return 图片访问URL
     */
    String uploadBackgroundImage(MultipartFile file);

    /**
     * 上传头像图片
     *
     * @param file 图片文件
     * @return 图片访问URL
     */
    String uploadAvatarImage(MultipartFile file);

    /**
     * 上传长图
     *
     * @param file 图片文件
     * @return 图片访问URL
     */
    String uploadLongImage(MultipartFile file);

    /**
     * 删除文件
     *
     * @param fileUrl 文件URL
     * @return 是否删除成功
     */
    boolean deleteFile(String fileUrl);

    /**
     * 验证图片文件
     *
     * @param file 文件
     * @return 是否为有效的图片文件
     */
    boolean isValidImageFile(MultipartFile file);

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 扩展名
     */
    String getFileExtension(String filename);

    /**
     * 生成唯一文件名
     *
     * @param originalFilename 原始文件名
     * @return 唯一文件名
     */
    String generateUniqueFilename(String originalFilename);
}
