package com.minmetals.config;

import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 数据库配置类
 * 解决PostgreSQL序列值溢出问题
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
public class DatabaseConfig {

    @Bean
    public HibernatePropertiesCustomizer hibernatePropertiesCustomizer() {
        return new HibernatePropertiesCustomizer() {
            @Override
            public void customize(Map<String, Object> hibernateProperties) {
                // 完全禁用数据库元数据访问，解决序列值溢出问题
                hibernateProperties.put("hibernate.boot.allow_jdbc_metadata_access", "false");
                hibernateProperties.put("hibernate.temp.use_jdbc_metadata_defaults", "false");
                hibernateProperties.put("hibernate.hbm2ddl.auto", "none");
                hibernateProperties.put("hibernate.jdbc.use_get_generated_keys", "false");
                hibernateProperties.put("hibernate.id.new_generator_mappings", "false");
            }
        };
    }
}
