package com.minmetals.config;

import com.minmetals.security.JwtAuthenticationEntryPoint;
import com.minmetals.security.JwtAuthenticationFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * Spring Security配置
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    @Lazy
    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable()
                .exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint).and()
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
                .authorizeRequests()
                // 允许访问的公共端点
                .antMatchers("/auth/login").permitAll()
                .antMatchers("/admin-auth/login").permitAll()
                .antMatchers("/admin-auth/verify").permitAll()
                .antMatchers("/admin-auth/test").permitAll()
                .antMatchers("/background/image").permitAll()
                .antMatchers("/files/**").permitAll() // 允许访问上传的文件
                .antMatchers("/upload/**").permitAll() // 允许访问文件上传接口
                .antMatchers("/file-upload/**").permitAll() // 允许访问文件上传接口
                .antMatchers("/employees/**").permitAll() // 临时允许员工管理接口
                .antMatchers("/background-images/**").permitAll() // 临时允许背景图片管理接口
                .antMatchers("/long-images/**").permitAll() // 临时允许长图管理接口
                .antMatchers("/departments/**").permitAll() // 临时允许部门管理接口
                .antMatchers("/actuator/health").permitAll()
                .antMatchers("/actuator/info").permitAll()
                // Swagger API文档相关路径
                .antMatchers("/swagger-ui/**").permitAll()
                .antMatchers("/swagger-ui.html").permitAll()
                .antMatchers("/swagger-resources/**").permitAll()
                .antMatchers("/v2/api-docs").permitAll()
                .antMatchers("/v3/api-docs/**").permitAll()
                .antMatchers("/webjars/**").permitAll()
                // 根路径
                .antMatchers("/").permitAll()
                .antMatchers("/api").permitAll()
                // 其他所有请求都需要认证
                .anyRequest().authenticated();

        // 添加JWT过滤器
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
