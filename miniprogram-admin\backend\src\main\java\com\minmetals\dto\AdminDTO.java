package com.minmetals.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理员DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Schema(description = "管理员信息")
public class AdminDTO {

    @Schema(description = "管理员ID")
    private Long id;

    @Schema(description = "管理员姓名")
    private String name;

    @Schema(description = "登录用户名")
    private String username;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "电话号码")
    private String phone;

    @Schema(description = "角色")
    private String role;

    @Schema(description = "是否启用")
    private Boolean enabled;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
