<template>
  <Layout>
    <div class="long-image-management">
      <div class="content-card">
        <div class="table-toolbar">
          <div class="toolbar-left">
            <h3>长图管理</h3>
          </div>
          <div class="toolbar-right">
            <!-- 移除上传按钮 -->
          </div>
        </div>

        <!-- 长图表格 -->
        <el-table
          :data="imageList"
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="title" label="标题" width="200" />
          <el-table-column label="图片预览" width="120">
            <template #default="{ row }">
              <el-image
                :src="row.imageUrl"
                :preview-src-list="[row.imageUrl]"
                class="table-image"
                fit="cover"
              />
            </template>
          </el-table-column>
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="creator" label="创建者" width="100" />
          <el-table-column prop="sortOrder" label="排序" width="80" />
          <el-table-column label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.enabled ? 'success' : 'danger'">
                {{ row.enabled ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="showEditDialog(row)"
              >
                修改
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 长图表单对话框 -->
      <el-dialog
        v-model="dialogVisible"
        title="修改长图"
        width="600px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
        >
          <el-form-item label="当前图片" v-if="form.imageUrl">
            <div class="current-image-preview">
              <el-image
                :src="form.imageUrl"
                :preview-src-list="[form.imageUrl]"
                style="max-width: 100%; max-height: 300px"
                fit="contain"
              />
            </div>
          </el-form-item>

          <el-form-item label="替换图片" prop="imageUrl">
            <ImageUpload
              v-model="form.imageUrl"
              upload-url="/file-upload/long-image"
              :limit="1"
              accept="image/*"
            />
          </el-form-item>
        </el-form>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">
              取消
            </el-button>
            <el-button
              type="primary"
              :loading="submitLoading"
              @click="handleSubmit"
            >
              {{ submitLoading ? '保存中...' : '保存' }}
            </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </Layout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import ImageUpload from '@/components/ImageUpload.vue'
import request from '@/utils/request'

const loading = ref(false)
const submitLoading = ref(false)
const imageList = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

const form = reactive({
  imageUrl: ''
})

const rules = {
  imageUrl: [
    { required: true, message: '请上传图片文件', trigger: 'change' }
  ]
}

// 加载长图列表
const loadImages = async () => {
  loading.value = true
  try {
    const response = await request.get('/long-images')
    imageList.value = response.data || []
  } catch (error) {
    console.error('加载长图列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 显示编辑对话框
const showEditDialog = (image) => {
  Object.assign(form, {
    imageUrl: image.imageUrl || ''
  })
  form.currentImageId = image.id // 保存当前图片ID用于更新
  isEdit.value = true
  dialogVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    // 只支持编辑模式，更新图片
    if (form.currentImageId) {
      await request.put(`/long-images/${form.currentImageId}`, {
        imageUrl: form.imageUrl
      })
      ElMessage.success('图片更新成功')
    }

    dialogVisible.value = false
    loadImages()
  } catch (error) {
    console.error('更新长图失败:', error)
    ElMessage.error('更新失败')
  } finally {
    submitLoading.value = false
  }
}



onMounted(() => {
  loadImages()
})
</script>

<style scoped>
.long-image-management {
  max-width: 1400px;
  margin: 0 auto;
}



.table-image {
  width: 80px;
  height: 50px;
  border-radius: 4px;
}

.current-image-preview {
  border: 1px solid var(--secondary-color);
  border-radius: 4px;
  padding: 10px;
  text-align: center;
  background: #f9f9f9;
}

h3 {
  color: var(--text-primary);
  margin: 0;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}
</style>
