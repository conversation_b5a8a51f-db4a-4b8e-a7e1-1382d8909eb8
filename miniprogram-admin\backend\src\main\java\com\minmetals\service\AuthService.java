package com.minmetals.service;

import com.minmetals.dto.LoginRequest;
import com.minmetals.dto.LoginResponse;

/**
 * 认证服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface AuthService {

    /**
     * 员工登录
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * 验证JWT令牌
     */
    boolean validateToken(String token);

    /**
     * 从令牌中获取用户名
     */
    String getUsernameFromToken(String token);

    /**
     * 从令牌中获取用户ID
     */
    Long getUserIdFromToken(String token);
}
