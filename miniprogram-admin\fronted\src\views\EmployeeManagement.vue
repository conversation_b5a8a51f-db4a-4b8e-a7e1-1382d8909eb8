<template>
  <Layout>
    <div class="employee-management">
      <div class="content-card">
        <div class="table-toolbar">
          <div class="toolbar-left">
            <h3>员工管理</h3>
          </div>
          <div class="toolbar-right">
            <el-button type="primary" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              新增员工
            </el-button>
          </div>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-bar">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input
                v-model="filters.keyword"
                placeholder="搜索员工姓名或用户名"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <DepartmentCascader
                v-model="filters.departmentId"
                placeholder="选择部门"
                @change="handleDepartmentFilter"
              />
            </el-col>
            <el-col :span="4">
              <el-select
                v-model="filters.position"
                placeholder="选择职位"
                clearable
                @change="handleFilter"
              >
                <el-option
                  v-for="pos in positions"
                  :key="pos"
                  :label="pos"
                  :value="pos"
                />
              </el-select>
            </el-col>
          </el-row>
        </div>

        <!-- 员工表格 -->
        <el-table
          :data="employeeList"
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="name" label="姓名" width="100" />
          <el-table-column prop="departmentPath" label="部门" width="200">
            <template #default="{ row }">
              {{ row.departmentPath || row.department }}
            </template>
          </el-table-column>
          <el-table-column prop="position" label="职位" width="120" />
          <el-table-column prop="email" label="邮箱" width="180" />
          <el-table-column prop="phone" label="电话" width="130" />
          <el-table-column label="头像" width="80">
            <template #default="{ row }">
              <el-avatar
                v-if="row.avatar"
                :src="row.avatar"
                :size="40"
              />
              <el-avatar v-else :size="40">
                {{ row.name?.charAt(0) }}
              </el-avatar>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.enabled ? 'success' : 'danger'">
                {{ row.enabled ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="showEditDialog(row)"
              >
                编辑
              </el-button>
              <el-button
                :type="row.enabled ? 'warning' : 'success'"
                size="small"
                @click="toggleEmployeeStatus(row)"
              >
                {{ row.enabled ? '禁用' : '启用' }}
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteEmployee(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 员工表单对话框 -->
      <EmployeeForm
        v-model:visible="dialogVisible"
        :employee="currentEmployee"
        :is-edit="isEdit"
        @success="handleFormSuccess"
      />
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import EmployeeForm from '@/components/EmployeeForm.vue'
import DepartmentCascader from '@/components/DepartmentCascader.vue'
import request from '@/utils/request'

const loading = ref(false)
const employeeList = ref([])
const dialogVisible = ref(false)
const currentEmployee = ref(null)
const isEdit = ref(false)

const filters = ref({
  keyword: '',
  departmentId: null,
  position: ''
})

const departments = ref([
  '技术部', '市场部', '人事部', '财务部', '运营部'
])

const positions = ref([
  '总监', '经理', '主管', '专员', '助理'
])

// 加载员工列表
const loadEmployees = async () => {
  loading.value = true
  try {
    let url = '/employees'
    const params = {}

    if (filters.value.keyword) {
      params.keyword = filters.value.keyword
    } else if (filters.value.departmentId) {
      url = `/employees/department-id/${filters.value.departmentId}`
    } else if (filters.value.position) {
      params.position = filters.value.position
    }

    const response = await request.get(url, { params })

    // 为每个员工构建部门路径
    const employees = response.data || []
    for (const employee of employees) {
      if (employee.departmentId) {
        try {
          const deptResponse = await request.get(`/departments/${employee.departmentId}`)
          employee.departmentPath = buildDepartmentPath(deptResponse.data)
        } catch (error) {
          console.error('获取部门路径失败:', error)
          employee.departmentPath = employee.department
        }
      }
    }

    employeeList.value = employees
  } catch (error) {
    console.error('加载员工列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 构建部门路径
const buildDepartmentPath = (department) => {
  if (!department) return ''

  // 如果有path字段，解析路径构建完整名称
  if (department.path) {
    // path格式如：/1/6/13，需要根据ID查询对应的部门名称
    // 简化实现，直接返回部门名称，实际项目中可以根据path递归查询
    return department.name
  }

  return department.name
}

// 搜索处理
const handleSearch = () => {
  loadEmployees()
}

// 筛选处理
const handleFilter = () => {
  loadEmployees()
}

// 部门筛选处理
const handleDepartmentFilter = (departmentId) => {
  filters.value.departmentId = departmentId
  loadEmployees()
}

// 显示创建对话框
const showCreateDialog = () => {
  currentEmployee.value = null
  isEdit.value = false
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (employee) => {
  currentEmployee.value = { ...employee }
  isEdit.value = true
  dialogVisible.value = true
}

// 表单成功处理
const handleFormSuccess = () => {
  dialogVisible.value = false
  loadEmployees()
}

// 切换员工状态
const toggleEmployeeStatus = async (employee) => {
  try {
    await ElMessageBox.confirm(
      `确定要${employee.enabled ? '禁用' : '启用'}员工 ${employee.name} 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await request.put(`/employees/${employee.id}/toggle-status`)
    ElMessage.success(`${employee.enabled ? '禁用' : '启用'}成功`)
    loadEmployees()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换员工状态失败:', error)
    }
  }
}

// 删除员工
const deleteEmployee = async (employee) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除员工 ${employee.name} 吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await request.delete(`/employees/${employee.id}`)
    ElMessage.success('删除成功')
    loadEmployees()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除员工失败:', error)
    }
  }
}

onMounted(() => {
  loadEmployees()
})
</script>

<style scoped>
.employee-management {
  max-width: 1400px;
  margin: 0 auto;
}

.filter-bar {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

h3 {
  color: var(--text-primary);
  margin: 0;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
}
</style>
