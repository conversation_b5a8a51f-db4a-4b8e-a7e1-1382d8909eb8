package com.minmetals.repository;

import com.minmetals.entity.BackgroundImage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 背景图片数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface BackgroundImageRepository extends JpaRepository<BackgroundImage, Long> {

    /**
     * 查找所有启用的背景图片，按排序顺序排列
     */
    List<BackgroundImage> findByEnabledTrueOrderBySortOrderAscCreatedAtDesc();

    /**
     * 查找第一张启用的背景图片
     */
    Optional<BackgroundImage> findFirstByEnabledTrueOrderBySortOrderAscCreatedAtDesc();

    /**
     * 根据名称查找背景图片
     */
    Optional<BackgroundImage> findByName(String name);

    /**
     * 检查名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 检查名称是否存在（排除指定ID）
     */
    boolean existsByNameAndIdNot(String name, Long id);

    /**
     * 模糊搜索背景图片（名称、描述）
     */
    @Query("SELECT b FROM BackgroundImage b WHERE " +
           "(LOWER(b.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(b.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY b.sortOrder ASC, b.createdAt DESC")
    List<BackgroundImage> searchBackgroundImages(@Param("keyword") String keyword);
}
