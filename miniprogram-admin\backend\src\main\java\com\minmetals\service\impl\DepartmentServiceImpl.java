package com.minmetals.service.impl;

import com.minmetals.dto.DepartmentCreateRequest;
import com.minmetals.dto.DepartmentDTO;
import com.minmetals.dto.DepartmentUpdateRequest;
import com.minmetals.entity.Department;
import com.minmetals.repository.DepartmentRepository;
import com.minmetals.repository.EmployeeRepository;
import com.minmetals.service.DepartmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 部门服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DepartmentServiceImpl implements DepartmentService {

    private final DepartmentRepository departmentRepository;
    private final EmployeeRepository employeeRepository;

    @Override
    @Transactional
    public DepartmentDTO createDepartment(DepartmentCreateRequest request) {
        log.info("创建部门: {}", request.getName());

        // 验证部门编码唯一性
        if (departmentRepository.findByCode(request.getCode()).isPresent()) {
            throw new RuntimeException("部门编码已存在: " + request.getCode());
        }

        // 验证父部门存在性和级别合理性
        if (request.getParentId() != null) {
            Department parentDept = departmentRepository.findById(request.getParentId())
                    .orElseThrow(() -> new RuntimeException("父部门不存在"));
            
            if (parentDept.getLevel() >= request.getLevel()) {
                throw new RuntimeException("子部门级别必须大于父部门级别");
            }
            
            if (request.getLevel() > 3) {
                throw new RuntimeException("部门级别不能超过3级");
            }
        } else if (request.getLevel() != 1) {
            throw new RuntimeException("一级部门的父部门ID必须为空");
        }

        Department department = new Department();
        BeanUtils.copyProperties(request, department);
        
        // 构建部门路径
        department.setPath(buildDepartmentPath(request.getParentId()));
        
        Department savedDepartment = departmentRepository.save(department);
        
        // 更新路径（包含自己的ID）
        savedDepartment.setPath(savedDepartment.getPath() + "/" + savedDepartment.getId());
        savedDepartment = departmentRepository.save(savedDepartment);

        return convertToDTO(savedDepartment);
    }

    @Override
    @Transactional
    public DepartmentDTO updateDepartment(Long id, DepartmentUpdateRequest request) {
        log.info("更新部门: {}", id);

        Department department = departmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("部门不存在"));

        BeanUtils.copyProperties(request, department, "id", "code", "parentId", "level", "path", "createdAt");
        
        Department savedDepartment = departmentRepository.save(department);
        return convertToDTO(savedDepartment);
    }

    @Override
    @Transactional
    public void deleteDepartment(Long id) {
        log.info("删除部门: {}", id);

        if (!canDeleteDepartment(id)) {
            throw new RuntimeException("该部门下还有子部门或员工，无法删除");
        }

        departmentRepository.deleteById(id);
    }

    @Override
    public DepartmentDTO getDepartmentById(Long id) {
        Department department = departmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("部门不存在"));
        return convertToDTO(department);
    }

    @Override
    public List<DepartmentDTO> getAllDepartments() {
        List<Department> departments = departmentRepository.findAll();
        return departments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DepartmentDTO> getDepartmentTree() {
        List<Department> allDepartments = departmentRepository.findByEnabledTrueOrderBySortOrderAsc();
        return buildDepartmentTree(allDepartments);
    }

    @Override
    public List<DepartmentDTO> getDepartmentsByLevel(Integer level) {
        List<Department> departments = departmentRepository.findByLevelAndEnabledTrueOrderBySortOrderAsc(level);
        return departments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DepartmentDTO> getChildDepartments(Long parentId) {
        List<Department> departments = departmentRepository.findByParentIdAndEnabledTrueOrderBySortOrderAsc(parentId);
        return departments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DepartmentDTO> getEnabledDepartments() {
        List<Department> departments = departmentRepository.findByEnabledTrueOrderBySortOrderAsc();
        return departments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DepartmentDTO> getEnabledDepartmentTree() {
        List<Department> allDepartments = departmentRepository.findByEnabledTrueOrderBySortOrderAsc();
        return buildDepartmentTree(allDepartments);
    }

    @Override
    public List<DepartmentDTO> searchDepartmentsByName(String name) {
        List<Department> departments = departmentRepository.findByNameContainingIgnoreCase(name);
        return departments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void toggleDepartmentStatus(Long id) {
        Department department = departmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("部门不存在"));
        
        department.setEnabled(!department.getEnabled());
        departmentRepository.save(department);
    }

    @Override
    public List<DepartmentDTO> getRootDepartments() {
        List<Department> departments = departmentRepository.findRootDepartments();
        return departments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<DepartmentDTO> getEnabledRootDepartments() {
        List<Department> departments = departmentRepository.findEnabledRootDepartments();
        return departments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public boolean isCodeUnique(String code, Long excludeId) {
        if (excludeId == null) {
            return !departmentRepository.findByCode(code).isPresent();
        }
        return !departmentRepository.existsByCodeAndIdNot(code, excludeId);
    }

    @Override
    public boolean canDeleteDepartment(Long id) {
        // 检查是否有子部门
        long childCount = departmentRepository.countByParentId(id);
        if (childCount > 0) {
            return false;
        }
        
        // 检查是否有员工
        long employeeCount = employeeRepository.countByDepartmentId(id);
        return employeeCount == 0;
    }

    @Override
    public DepartmentDTO convertToDTO(Department department) {
        DepartmentDTO dto = new DepartmentDTO();
        BeanUtils.copyProperties(department, dto);
        
        // 设置父部门名称
        if (department.getParentId() != null) {
            departmentRepository.findById(department.getParentId())
                    .ifPresent(parent -> dto.setParentName(parent.getName()));
        }
        
        // 设置员工数量
        long employeeCount = employeeRepository.countByDepartmentId(department.getId());
        dto.setEmployeeCount(employeeCount);
        
        return dto;
    }

    @Override
    public String buildDepartmentPath(Long parentId) {
        if (parentId == null) {
            return "";
        }
        
        Department parent = departmentRepository.findById(parentId)
                .orElseThrow(() -> new RuntimeException("父部门不存在"));
        
        return parent.getPath();
    }

    /**
     * 构建部门树形结构
     */
    private List<DepartmentDTO> buildDepartmentTree(List<Department> departments) {
        List<DepartmentDTO> dtoList = departments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        Map<Long, List<DepartmentDTO>> groupByParent = dtoList.stream()
                .filter(dto -> dto.getParentId() != null)
                .collect(Collectors.groupingBy(DepartmentDTO::getParentId));

        // 设置子部门
        dtoList.forEach(dto -> {
            List<DepartmentDTO> children = groupByParent.get(dto.getId());
            dto.setChildren(children != null ? children : new ArrayList<>());
        });

        // 返回根部门
        return dtoList.stream()
                .filter(dto -> dto.getParentId() == null)
                .collect(Collectors.toList());
    }
}
