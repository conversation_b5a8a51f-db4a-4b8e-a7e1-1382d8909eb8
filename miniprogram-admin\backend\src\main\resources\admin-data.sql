-- 管理员数据初始化脚本
-- 注意：密码使用明文存储，所有用户的密码都是 "123456"

-- 插入管理员数据（如果不存在）
INSERT INTO admins (name, username, password, email, phone, role, enabled, description, created_at, updated_at) 
SELECT '超级管理员', 'superadmin', '123456', '<EMAIL>', '86-10-68494100', 'SUPER_ADMIN', true, '系统超级管理员，拥有所有权限', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM admins WHERE username = 'superadmin');

INSERT INTO admins (name, username, password, email, phone, role, enabled, description, created_at, updated_at) 
SELECT '系统管理员', 'admin', '123456', '<EMAIL>', '86-10-68494101', 'ADMIN', true, '系统管理员，负责日常管理工作', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM admins WHERE username = 'admin');

INSERT INTO admins (name, username, password, email, phone, role, enabled, description, created_at, updated_at) 
SELECT '人事管理员', 'hradmin', '123456', '<EMAIL>', '86-10-68494102', 'ADMIN', true, '人事管理员，负责员工管理', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM admins WHERE username = 'hradmin');
