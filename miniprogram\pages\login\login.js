// pages/login/login.js
const { api } = require('../../utils/api')

Page({
  data: {
    username: '',
    password: '',
    isLoading: false,
    canSubmit: false
  },

  onLoad() {
    // 检查是否已登录
    const app = getApp()
    if (app.globalData.isLoggedIn) {
      wx.redirectTo({
        url: '/pages/employee-list/employee-list'
      })
    }
  },

  /**
   * 用户名输入
   */
  onUsernameInput(e) {
    const username = e.detail.value.trim()
    this.setData({
      username
    })
    this.checkCanSubmit()
  },

  /**
   * 密码输入
   */
  onPasswordInput(e) {
    const password = e.detail.value.trim()
    this.setData({
      password
    })
    this.checkCanSubmit()
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit() {
    const { username, password } = this.data
    const canSubmit = username.length > 0 && password.length > 0
    this.setData({
      canSubmit
    })
  },

  /**
   * 处理登录
   */
  async handleLogin() {
    const { username, password, isLoading, canSubmit } = this.data

    if (isLoading || !canSubmit) {
      return
    }

    // 基本验证
    if (!username || !password) {
      wx.showToast({
        title: '请输入用户名和密码',
        icon: 'none'
      })
      return
    }

    if (username.length < 2) {
      wx.showToast({
        title: '用户名至少2个字符',
        icon: 'none'
      })
      return
    }

    if (password.length < 6) {
      wx.showToast({
        title: '密码至少6个字符',
        icon: 'none'
      })
      return
    }

    this.setData({
      isLoading: true
    })

    try {
      wx.showLoading({
        title: '登录中...'
      })

      const result = await api.login(username, password)

      if (result.code === 200 && result.data) {
        const { token, employeeInfo } = result.data

        // 保存登录信息
        const app = getApp()
        app.login(employeeInfo, token)

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 跳转到员工列表页
        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/employee-list/employee-list'
          })
        }, 1500)

      } else {
        wx.showToast({
          title: result.message || '登录失败',
          icon: 'none'
        })
      }

    } catch (error) {
      console.error('登录失败:', error)
      
      let errorMessage = '登录失败，请重试'
      
      if (error.message) {
        errorMessage = error.message
      } else if (error.data && error.data.message) {
        errorMessage = error.data.message
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      })

    } finally {
      this.setData({
        isLoading: false
      })
      wx.hideLoading()
    }
  },

  /**
   * 返回首页
   */
  goBack() {
    wx.navigateBack()
  },

  /**
   * 清空表单
   */
  clearForm() {
    this.setData({
      username: '',
      password: '',
      canSubmit: false
    })
  }
})
