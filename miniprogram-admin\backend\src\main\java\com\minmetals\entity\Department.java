package com.minmetals.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门实体类 - 支持3级部门结构
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "departments")
@EntityListeners(AuditingEntityListener.class)
public class Department {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 部门名称
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    /**
     * 部门编码
     */
    @Column(name = "code", nullable = false, unique = true, length = 50)
    private String code;

    /**
     * 父部门ID（一级部门为null）
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 部门级别（1-一级部门，2-二级部门，3-三级部门）
     */
    @Column(name = "level", nullable = false)
    private Integer level;

    /**
     * 部门路径（如：1/2/3，便于查询）
     */
    @Column(name = "path", length = 500)
    private String path;

    /**
     * 部门描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 部门负责人
     */
    @Column(name = "manager", length = 50)
    private String manager;

    /**
     * 联系电话
     */
    @Column(name = "phone", length = 20)
    private String phone;

    /**
     * 联系邮箱
     */
    @Column(name = "email", length = 100)
    private String email;

    /**
     * 办公地址
     */
    @Column(name = "address", length = 200)
    private String address;

    /**
     * 排序号
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 子部门列表（用于树形结构展示，不持久化）
     */
    @Transient
    private List<Department> children;

    /**
     * 父部门名称（用于显示，不持久化）
     */
    @Transient
    private String parentName;

    /**
     * 员工数量（用于统计，不持久化）
     */
    @Transient
    private Long employeeCount;
}
