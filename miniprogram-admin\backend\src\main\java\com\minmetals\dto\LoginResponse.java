package com.minmetals.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 登录响应DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "登录响应")
public class LoginResponse {

    @Schema(description = "JWT令牌")
    private String token;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "用户姓名")
    private String name;

    @Schema(description = "用户角色")
    private String role;

    @Schema(description = "用户类型", allowableValues = {"EMPLOYEE", "ADMIN"})
    private String userType;

    @Schema(description = "员工信息（仅当userType为EMPLOYEE时有值）")
    private EmployeeDTO employeeInfo;
}
