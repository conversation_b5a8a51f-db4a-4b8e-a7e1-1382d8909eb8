package com.minmetals.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 长图展示DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class LongImageDTO {

    /**
     * 长图ID
     */
    private Long id;

    /**
     * 长图分类
     */
    private String category;

    /**
     * 长图标题
     */
    private String title;

    /**
     * 长图URL
     */
    private String imageUrl;

    /**
     * 长图描述
     */
    private String description;

    /**
     * 图片宽度（像素）
     */
    private Integer width;

    /**
     * 图片高度（像素）
     */
    private Integer height;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 图片格式
     */
    private String format;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;
}
