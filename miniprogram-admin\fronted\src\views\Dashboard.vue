<template>
  <Layout>
    <div class="dashboard">
      <div class="content-card">
        <h2>欢迎使用中国五矿管理平台</h2>
        <p>当前登录用户：{{ authStore.name || authStore.username }}</p>
        <p>用户角色：{{ authStore.role }}</p>
      </div>

      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon employee">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.employeeCount }}</div>
              <div class="stat-label">员工总数</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon background">
              <el-icon><Picture /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.backgroundImageCount }}</div>
              <div class="stat-label">背景图片</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon longimage">
              <el-icon><PictureRounded /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.longImageCount }}</div>
              <div class="stat-label">长图数量</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon department">
              <el-icon><OfficeBuilding /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.departmentCount }}</div>
              <div class="stat-label">部门数量</div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 快捷操作 -->
      <div class="content-card">
        <h3>快捷操作</h3>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-button type="primary" @click="$router.push('/departments')" style="width: 100%">
              <el-icon><OfficeBuilding /></el-icon>
              部门管理
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="info" @click="$router.push('/employees')" style="width: 100%">
              <el-icon><User /></el-icon>
              员工管理
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="success" @click="$router.push('/background-images')" style="width: 100%">
              <el-icon><Picture /></el-icon>
              背景图片
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="warning" @click="$router.push('/long-images')" style="width: 100%">
              <el-icon><PictureRounded /></el-icon>
              长图管理
            </el-button>
          </el-col>
        </el-row>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { User, Picture, PictureRounded, OfficeBuilding } from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/store/auth'
import request from '@/utils/request'

const authStore = useAuthStore()

const stats = ref({
  employeeCount: 0,
  backgroundImageCount: 0,
  longImageCount: 0,
  departmentCount: 0
})

const loadStats = async () => {
  try {
    // 获取员工统计
    const employeeResponse = await request.get('/employees')
    stats.value.employeeCount = employeeResponse.data?.length || 0

    // 获取部门统计
    const departmentResponse = await request.get('/departments')
    stats.value.departmentCount = departmentResponse.data?.length || 0

    // 获取背景图片统计
    const backgroundResponse = await request.get('/background-images')
    stats.value.backgroundImageCount = backgroundResponse.data?.length || 0

    // 获取长图统计
    const longImageResponse = await request.get('/long-images')
    stats.value.longImageCount = longImageResponse.data?.length || 0
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 使用模拟数据作为后备
    stats.value = {
      employeeCount: 25,
      backgroundImageCount: 8,
      longImageCount: 15,
      departmentCount: 12
    }
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.employee {
  background: #409eff;
}

.stat-icon.background {
  background: #67c23a;
}

.stat-icon.longimage {
  background: #e6a23c;
}

.stat-icon.department {
  background: var(--primary-color);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: 5px;
}

h2 {
  color: var(--text-primary);
  margin-bottom: 20px;
}

h3 {
  color: var(--text-primary);
  margin-bottom: 15px;
}

p {
  color: var(--text-secondary);
  margin-bottom: 10px;
}
</style>
