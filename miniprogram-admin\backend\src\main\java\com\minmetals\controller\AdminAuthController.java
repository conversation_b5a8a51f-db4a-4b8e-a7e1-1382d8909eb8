package com.minmetals.controller;

import com.minmetals.dto.ApiResponse;
import com.minmetals.dto.LoginRequest;
import com.minmetals.dto.LoginResponse;
import com.minmetals.entity.Admin;
import com.minmetals.service.AdminService;
import com.minmetals.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 管理员认证控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/admin-auth")
@RequiredArgsConstructor
@Tag(name = "管理员认证", description = "管理员登录相关接口")
public class AdminAuthController {

    private final AdminService adminService;
    private final JwtUtil jwtUtil;

    /**
     * 测试管理员数据
     */
    @GetMapping("/test")
    @Operation(summary = "测试管理员数据", description = "测试管理员数据是否存在")
    public ApiResponse<String> testAdminData() {
        log.info("测试管理员数据");
        try {
            Admin admin = adminService.findByUsername("admin");
            if (admin == null) {
                return ApiResponse.error("管理员数据不存在");
            }
            return ApiResponse.success("管理员数据存在", "找到管理员: " + admin.getName());
        } catch (Exception e) {
            log.error("测试管理员数据时发生异常", e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 管理员登录
     */
    @PostMapping("/login")
    @Operation(summary = "管理员登录", description = "管理员用户名密码登录")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        log.info("管理员登录请求，用户名: {}", loginRequest.getUsername());

        try {
            // 首先检查管理员是否存在
            Admin admin = adminService.findByUsername(loginRequest.getUsername());
            if (admin == null) {
                log.warn("管理员 {} 登录失败: 用户不存在", loginRequest.getUsername());
                return ApiResponse.error("用户不存在");
            }

            // 验证管理员登录
            boolean isValid = adminService.validateLogin(loginRequest.getUsername(), loginRequest.getPassword());

            if (!isValid) {
                log.warn("管理员 {} 登录失败: 用户名或密码错误", loginRequest.getUsername());
                return ApiResponse.error("用户名或密码错误");
            }

            if (!admin.getEnabled()) {
                log.warn("管理员 {} 登录失败: 账户已被禁用", loginRequest.getUsername());
                return ApiResponse.error("账户已被禁用");
            }

            // 生成JWT令牌
            String token = jwtUtil.generateToken(admin.getUsername(), "ADMIN");

            // 更新最后登录时间
            adminService.updateLastLoginTime(admin.getUsername());

            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setToken(token);
            response.setUsername(admin.getUsername());
            response.setName(admin.getName());
            response.setRole(admin.getRole());
            response.setUserType("ADMIN");

            log.info("管理员 {} 登录成功", admin.getUsername());
            return ApiResponse.success("登录成功", response);

        } catch (Exception e) {
            log.error("管理员 {} 登录时发生异常", loginRequest.getUsername(), e);
            return ApiResponse.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 管理员登出
     */
    @PostMapping("/logout")
    @Operation(summary = "管理员登出", description = "管理员登出（清除客户端token）")
    public ApiResponse<String> logout() {
        log.info("管理员登出");
        // 由于使用JWT，服务端无状态，登出主要由客户端处理
        return ApiResponse.success("登出成功", "请清除客户端存储的token");
    }

    /**
     * 验证管理员token
     */
    @GetMapping("/verify")
    @Operation(summary = "验证管理员token", description = "验证管理员JWT token是否有效")
    public ApiResponse<LoginResponse> verifyToken(@RequestHeader("Authorization") String authHeader) {
        log.info("验证管理员token");

        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ApiResponse.error("无效的Authorization头");
            }

            String token = authHeader.substring(7);
            String username = jwtUtil.extractUsername(token);

            if (username == null || !jwtUtil.validateToken(token, username)) {
                return ApiResponse.error("无效的token");
            }

            // 获取管理员信息
            Admin admin = adminService.findByUsername(username);
            if (admin == null || !admin.getEnabled()) {
                return ApiResponse.error("用户不存在或已被禁用");
            }

            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setToken(token);
            response.setUsername(admin.getUsername());
            response.setName(admin.getName());
            response.setRole(admin.getRole());
            response.setUserType("ADMIN");

            return ApiResponse.success("token验证成功", response);

        } catch (Exception e) {
            log.error("验证管理员token时发生异常", e);
            return ApiResponse.error("token验证失败: " + e.getMessage());
        }
    }
}
