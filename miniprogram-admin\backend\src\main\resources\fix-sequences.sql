-- 修复PostgreSQL序列值溢出问题
-- 这个脚本用于检查和修复数据库中的序列值

-- 1. 查看所有序列的当前值
SELECT 
    schemaname,
    sequencename,
    last_value,
    max_value,
    increment_by
FROM pg_sequences 
WHERE schemaname = 'public';

-- 2. 重置可能有问题的序列
-- 注意：执行前请备份数据库

-- 重置employees表的序列
DO $$
DECLARE
    max_id BIGINT;
BEGIN
    -- 获取employees表的最大ID
    SELECT COALESCE(MAX(id), 0) + 1 INTO max_id FROM employees;
    
    -- 重置序列
    EXECUTE 'ALTER SEQUENCE employees_id_seq RESTART WITH ' || max_id;
    
    RAISE NOTICE '已重置employees_id_seq序列，新起始值: %', max_id;
END $$;

-- 重置departments表的序列（如果存在）
DO $$
DECLARE
    max_id BIGINT;
BEGIN
    -- 检查departments表是否存在
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'departments') THEN
        -- 获取departments表的最大ID
        SELECT COALESCE(MAX(id), 0) + 1 INTO max_id FROM departments;
        
        -- 重置序列
        EXECUTE 'ALTER SEQUENCE departments_id_seq RESTART WITH ' || max_id;
        
        RAISE NOTICE '已重置departments_id_seq序列，新起始值: %', max_id;
    END IF;
END $$;

-- 重置background_images表的序列（如果存在）
DO $$
DECLARE
    max_id BIGINT;
BEGIN
    -- 检查background_images表是否存在
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'background_images') THEN
        -- 获取background_images表的最大ID
        SELECT COALESCE(MAX(id), 0) + 1 INTO max_id FROM background_images;
        
        -- 重置序列
        EXECUTE 'ALTER SEQUENCE background_images_id_seq RESTART WITH ' || max_id;
        
        RAISE NOTICE '已重置background_images_id_seq序列，新起始值: %', max_id;
    END IF;
END $$;

-- 重置long_images表的序列（如果存在）
DO $$
DECLARE
    max_id BIGINT;
BEGIN
    -- 检查long_images表是否存在
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'long_images') THEN
        -- 获取long_images表的最大ID
        SELECT COALESCE(MAX(id), 0) + 1 INTO max_id FROM long_images;
        
        -- 重置序列
        EXECUTE 'ALTER SEQUENCE long_images_id_seq RESTART WITH ' || max_id;
        
        RAISE NOTICE '已重置long_images_id_seq序列，新起始值: %', max_id;
    END IF;
END $$;

-- 3. 查看修复后的序列状态
SELECT 
    schemaname,
    sequencename,
    last_value,
    max_value,
    increment_by
FROM pg_sequences 
WHERE schemaname = 'public';

-- 4. 如果序列值仍然异常，可以手动设置最大值
-- ALTER SEQUENCE employees_id_seq MAXVALUE 9223372036854775807;
-- ALTER SEQUENCE departments_id_seq MAXVALUE 9223372036854775807;
-- ALTER SEQUENCE background_images_id_seq MAXVALUE 9223372036854775807;
-- ALTER SEQUENCE long_images_id_seq MAXVALUE 9223372036854775807;
