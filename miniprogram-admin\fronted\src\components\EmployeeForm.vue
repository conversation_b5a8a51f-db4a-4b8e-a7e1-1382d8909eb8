<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑员工' : '新增员工'"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="form.username"
              :disabled="isEdit"
              placeholder="请输入用户名"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入姓名"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门" prop="departmentId">
            <DepartmentCascader
              v-model="form.departmentId"
              @change="handleDepartmentChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职位" prop="position">
            <el-select
              v-model="form.position"
              placeholder="请选择职位"
              style="width: 100%"
            >
              <el-option
                v-for="pos in positions"
                :key="pos"
                :label="pos"
                :value="pos"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="form.email"
              placeholder="请输入邮箱"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="phone">
            <el-input
              v-model="form.phone"
              placeholder="请输入电话"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item v-if="!isEdit" label="密码" prop="password">
        <el-input
          v-model="form.password"
          type="password"
          placeholder="请输入密码"
          show-password
        />
      </el-form-item>

      <el-form-item label="头像">
        <ImageUpload
          v-model="form.avatar"
          :limit="1"
          accept="image/*"
          upload-url="/file-upload/avatar-image"
        />
      </el-form-item>

      <el-form-item label="个人简介">
        <el-input
          v-model="form.bio"
          type="textarea"
          :rows="3"
          placeholder="请输入个人简介"
        />
      </el-form-item>

      <el-form-item label="状态">
        <el-switch
          v-model="form.enabled"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ loading ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import ImageUpload from '@/components/ImageUpload.vue'
import DepartmentCascader from '@/components/DepartmentCascader.vue'
import request from '@/utils/request'

const props = defineProps({
  visible: Boolean,
  employee: Object,
  isEdit: Boolean
})

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref()
const loading = ref(false)

const form = reactive({
  username: '',
  name: '',
  departmentId: null,
  department: '',
  position: '',
  email: '',
  phone: '',
  password: '',
  avatar: '',
  bio: '',
  enabled: true
})

const departments = ref([
  '技术部', '市场部', '人事部', '财务部', '运营部'
])

const positions = ref([
  '总监', '经理', '主管', '专员', '助理'
])

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  departmentId: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  position: [
    { required: true, message: '请选择职位', trigger: 'change' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 处理部门变化
const handleDepartmentChange = async (departmentId) => {
  if (departmentId) {
    try {
      const response = await request.get(`/departments/${departmentId}`)
      form.department = response.data.name
    } catch (error) {
      console.error('获取部门信息失败:', error)
    }
  } else {
    form.department = ''
  }
}

// 监听员工数据变化
watch(() => props.employee, (newEmployee) => {
  if (newEmployee) {
    Object.assign(form, {
      username: newEmployee.username || '',
      name: newEmployee.name || '',
      departmentId: newEmployee.departmentId || null,
      department: newEmployee.department || '',
      position: newEmployee.position || '',
      email: newEmployee.email || '',
      phone: newEmployee.phone || '',
      password: '',
      avatar: newEmployee.avatar || '',
      bio: newEmployee.bio || '',
      enabled: newEmployee.enabled !== false
    })
  } else {
    // 重置表单
    Object.assign(form, {
      username: '',
      name: '',
      departmentId: null,
      department: '',
      position: '',
      email: '',
      phone: '',
      password: '',
      avatar: '',
      bio: '',
      enabled: true
    })
  }
}, { immediate: true })

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const submitData = { ...form }
    
    // 如果是编辑模式且没有输入新密码，则不提交密码字段
    if (props.isEdit && !submitData.password) {
      delete submitData.password
    }

    if (props.isEdit) {
      await request.put(`/employees/${props.employee.id}`, submitData)
      ElMessage.success('更新成功')
    } else {
      await request.post('/employees', submitData)
      ElMessage.success('创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('保存员工失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
