package com.minmetals.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 员工长图更新请求DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Schema(description = "员工长图更新请求")
public class EmployeeLongImageUpdateRequest {

    @Schema(description = "长图标题")
    @Size(max = 100, message = "长图标题长度不能超过100个字符")
    private String title;

    @Schema(description = "长图URL")
    @Size(max = 500, message = "长图URL长度不能超过500个字符")
    private String imageUrl;

    @Schema(description = "长图描述")
    @Size(max = 1000, message = "长图描述长度不能超过1000个字符")
    private String description;

    @Schema(description = "图片宽度（像素）")
    private Integer width;

    @Schema(description = "图片高度（像素）")
    private Integer height;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "图片格式")
    @Size(max = 10, message = "图片格式长度不能超过10个字符")
    private String format;

    @Schema(description = "是否启用")
    private Boolean enabled;

    @Schema(description = "排序顺序")
    private Integer sortOrder;
}
