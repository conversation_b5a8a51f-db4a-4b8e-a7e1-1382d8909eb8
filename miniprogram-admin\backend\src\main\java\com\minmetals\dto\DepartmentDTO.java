package com.minmetals.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门数据传输对象
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class DepartmentDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门编码
     */
    private String code;

    /**
     * 父部门ID
     */
    private Long parentId;

    /**
     * 父部门名称
     */
    private String parentName;

    /**
     * 部门级别
     */
    private Integer level;

    /**
     * 部门路径
     */
    private String path;

    /**
     * 部门描述
     */
    private String description;

    /**
     * 部门负责人
     */
    private String manager;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 办公地址
     */
    private String address;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 员工数量
     */
    private Long employeeCount;

    /**
     * 子部门列表
     */
    private List<DepartmentDTO> children;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
