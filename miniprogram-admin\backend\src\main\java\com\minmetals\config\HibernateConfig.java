package com.minmetals.config;

import org.hibernate.boot.MetadataBuilder;
import org.hibernate.boot.spi.MetadataBuilderContributor;
import org.springframework.context.annotation.Configuration;

/**
 * Hibernate配置类
 * 解决PostgreSQL序列值溢出问题
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
public class HibernateConfig implements MetadataBuilderContributor {

    @Override
    public void contribute(MetadataBuilder metadataBuilder) {
        // 禁用序列信息提取，避免序列值溢出问题
        metadataBuilder.applyImplicitSchemaName("public");
    }
}
