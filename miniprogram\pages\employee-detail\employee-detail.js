// pages/employee-detail/employee-detail.js
const { api } = require('../../utils/api')

Page({
  data: {
    employee: {},
    loading: true,
    employeeId: null,
    longImageData: null,
    longImageLoading: false
  },

  onLoad(options) {
    const { id } = options

    if (!id) {
      wx.showToast({
        title: '员工ID不能为空',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({
      employeeId: id
    })

    this.checkLoginStatus()
    this.loadEmployeeDetail(id)
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp()
    if (!app.globalData.isLoggedIn) {
      wx.redirectTo({
        url: '/pages/login/login'
      })
      return
    }
  },

  /**
   * 加载员工详情
   */
  async loadEmployeeDetail(id) {
    try {
      this.setData({
        loading: true
      })

      wx.showLoading({
        title: '加载中...'
      })

      const result = await api.getEmployeeDetail(id)

      if (result.code === 200 && result.data) {
        const employee = result.data

        // 格式化日期
        if (employee.hireDate) {
          employee.hireDate = this.formatDate(employee.hireDate)
        }

        this.setData({
          employee,
          loading: false
        })

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: employee.name || '员工详情'
        })

        // 加载长图（固定加载ID为1的长图）
        this.loadLongImage(1)

      } else {
        throw new Error(result.message || '获取员工详情失败')
      }

    } catch (error) {
      console.error('加载员工详情失败:', error)

      this.setData({
        loading: false,
        employee: {}
      })

      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none',
        duration: 3000
      })

      // 3秒后返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 3000)

    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 加载长图
   */
  async loadLongImage(longImageId) {
    try {
      this.setData({
        longImageLoading: true
      })

      const result = await api.getLongImageById(longImageId)

      if (result.code === 200 && result.data) {
        this.setData({
          longImageData: result.data,
          longImageLoading: false
        })
      } else {
        // 没有长图或获取失败，不显示错误信息
        this.setData({
          longImageLoading: false
        })
      }

    } catch (error) {
      console.log('获取长图失败:', error)
      // 静默处理错误，不显示错误提示
      this.setData({
        longImageLoading: false
      })
    }
  },

  /**
   * 长图加载成功
   */
  onLongImageLoad(e) {
    console.log('长图加载成功:', e.detail)
  },

  /**
   * 长图加载失败
   */
  onLongImageError(e) {
    console.error('长图加载失败:', e.detail)
    wx.showToast({
      title: '图片加载失败',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 格式化日期
   */
  formatDate(dateString) {
    if (!dateString) return ''

    try {
      const date = new Date(dateString)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    } catch (error) {
      console.error('日期格式化失败:', error)
      return dateString
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack()
  },

  /**
   * 刷新数据
   */
  onPullDownRefresh() {
    const { employeeId } = this.data
    if (employeeId) {
      this.loadEmployeeDetail(employeeId).finally(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    const { employee } = this.data
    return {
      title: `${employee.name || '员工'} - 中国五矿`,
      path: `/pages/employee-detail/employee-detail?id=${employee.id}`
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { employee } = this.data
    return {
      title: `${employee.name || '员工'} - 中国五矿`,
      imageUrl: employee.avatar
    }
  },

  /**
   * 处理打电话
   */
  handlePhoneCall() {
    const { employee } = this.data
    if (!employee.avatar) {
      wx.showToast({
        title: '暂无电话号码',
        icon: 'none'
      })
      return
    }
    wx.makePhoneCall({
      phoneNumber: employee.avatar,
      fail(err) {
        console.error('拨打电话失败:', err)
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 处理分享名片
   */
  handleShareCard() {
    const { employee } = this.data
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  /**
   * 处理存入通讯录
   */
  handleSaveContact() {
    const { employee } = this.data
    wx.addPhoneContact({
      firstName: employee.name,
      mobilePhoneNumber: employee.avatar,
      workPhoneNumber: employee.avatar,
      email: employee.email,
      organization: employee.department,
      title: employee.position,
      address: employee.address,
      success() {
        wx.showToast({
          title: '已添加到通讯录',
          icon: 'success'
        })
      },
      fail(err) {
        console.error('添加到通讯录失败:', err)
        wx.showToast({
          title: '添加到通讯录失败',
          icon: 'none'
        })
      }
    })
  }
})
