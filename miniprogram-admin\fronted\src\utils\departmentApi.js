import request from './request'

/**
 * 部门管理API
 */
export const departmentApi = {
  // 获取所有部门
  getAllDepartments() {
    return request.get('/departments')
  },

  // 获取部门树形结构
  getDepartmentTree() {
    return request.get('/departments/tree')
  },

  // 获取启用状态的部门树形结构
  getEnabledDepartmentTree() {
    return request.get('/departments/enabled/tree')
  },

  // 根据ID获取部门
  getDepartmentById(id) {
    return request.get(`/departments/${id}`)
  },

  // 根据级别获取部门
  getDepartmentsByLevel(level) {
    return request.get(`/departments/level/${level}`)
  },

  // 获取子部门
  getChildDepartments(parentId) {
    return request.get(`/departments/parent/${parentId}`)
  },

  // 搜索部门
  searchDepartments(name) {
    return request.get('/departments/search', { params: { name } })
  },

  // 创建部门
  createDepartment(data) {
    return request.post('/departments', data)
  },

  // 更新部门
  updateDepartment(id, data) {
    return request.put(`/departments/${id}`, data)
  },

  // 删除部门
  deleteDepartment(id) {
    return request.delete(`/departments/${id}`)
  },

  // 切换部门状态
  toggleDepartmentStatus(id) {
    return request.put(`/departments/${id}/toggle-status`)
  },

  // 检查部门编码唯一性
  checkCodeUnique(code, excludeId = null) {
    return request.get('/departments/code/check', {
      params: { code, excludeId }
    })
  },

  // 检查部门是否可以删除
  canDeleteDepartment(id) {
    return request.get(`/departments/${id}/can-delete`)
  },

  // 获取一级部门
  getRootDepartments() {
    return request.get('/departments/root')
  },

  // 获取启用的一级部门
  getEnabledRootDepartments() {
    return request.get('/departments/root/enabled')
  }
}

/**
 * 员工部门相关API
 */
export const employeeDepartmentApi = {
  // 根据部门ID获取员工
  getEmployeesByDepartmentId(departmentId) {
    return request.get(`/employees/department-id/${departmentId}`)
  },

  // 根据部门ID获取启用状态的员工
  getEnabledEmployeesByDepartmentId(departmentId) {
    return request.get(`/employees/department-id/${departmentId}/enabled`)
  }
}

export default {
  ...departmentApi,
  ...employeeDepartmentApi
}
