package com.minmetals.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.Size;

/**
 * 管理员更新请求DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Schema(description = "管理员更新请求")
public class AdminUpdateRequest {

    @Schema(description = "管理员姓名")
    @Size(max = 50, message = "管理员姓名长度不能超过50个字符")
    private String name;

    @Schema(description = "登录密码")
    @Size(min = 6, max = 255, message = "密码长度必须在6-255个字符之间")
    private String password;

    @Schema(description = "邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    @Schema(description = "电话号码")
    @Size(max = 20, message = "电话号码长度不能超过20个字符")
    private String phone;

    @Schema(description = "角色", allowableValues = {"SUPER_ADMIN", "ADMIN"})
    private String role;

    @Schema(description = "是否启用")
    private Boolean enabled;

    @Schema(description = "描述")
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
}
